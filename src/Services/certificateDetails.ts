import { createDoc, readCollection, updateDocFields, deleteDocByPath, readDoc } from "./firestoreService";
import { type QuestionInput, type QuestionRecord } from "./questionsService";

export type CertificateQuestionType =
  | "multiple_choice"
  | "graded_multiple_choice"
  | "text_input"
  | "mix";

export type CertificateDomain =
  | "data_management"
  | "enterprise_architecture"
  | "it_governance";

export interface CertificateDetailsInput {
  name: string;
  provider: string;
  description: string;
  backgroundUrl: string;
  questionType: CertificateQuestionType;
  domain: CertificateDomain;
  createdByUid: string;
}

// Question types are now imported from questionsService

// Creates a new certificate document under certificates/{slug}
// The slug is derived from the certificate name and ensured unique by suffixing -2, -3, ... if needed
export async function createCertificateDetails(
  input: CertificateDetailsInput
): Promise<string> {
  const baseSlug = slugify(input.name);
  const id = await ensureUniqueCertificateId(baseSlug);
  await createDoc(`certificates/${id}`, { id, ...input });
  return id;
}

function slugify(value: string): string {
  return value
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "")
    .slice(0, 120) || "certificate"; // fallback in case name is empty after cleanup
}

async function ensureUniqueCertificateId(base: string): Promise<string> {
  let candidate = base;
  let suffix = 2;
  // Try base, then base-2, base-3, ... until a free id is found
  // Hard stop after a reasonable number of attempts to avoid infinite loops
  for (let i = 0; i < 100; i++) {
    const exists = await readDoc(`certificates/${candidate}`);
    if (!exists) return candidate;
    candidate = `${base}-${suffix++}`;
  }
  // As a last resort, append a timestamp
  return `${base}-${Date.now()}`;
}

export type CertificateRecord = CertificateDetailsInput & { id: string; createdAt?: any };

export async function listCertificates(): Promise<CertificateRecord[]> {
  const docs = await readCollection<CertificateRecord>(`certificates`);
  return docs.sort((a, b) => Number(new Date(b.createdAt || 0)) - Number(new Date(a.createdAt || 0)));
}

export async function getCertificate(id: string): Promise<CertificateRecord | null> {
  const doc = await readDoc<CertificateRecord>(`certificates/${id}`);
  return doc as CertificateRecord | null;
}

export async function updateCertificate(id: string, data: Partial<CertificateDetailsInput>): Promise<void> {
  await updateDocFields<CertificateRecord>(`certificates/${id}`, data);
}

export async function deleteCertificate(id: string): Promise<void> {
  await deleteDocByPath(`certificates/${id}`);
}


