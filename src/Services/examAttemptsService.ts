import { createDoc, readCollection, updateDocFields, deleteDocByPath, readDoc } from "./firestoreService";
import { type Timestamp } from "firebase/firestore";

export type ExamMode = "practice" | "realExam" | "questionBank";
export type AttemptStatus = "inProgress" | "completed" | "abandoned";
export type Difficulty = "Easy" | "Medium" | "Hard" | "All";

export interface ExamConfiguration {
  mode: ExamMode;
  numberOfQuestions?: number; // Not applicable for questionBank mode
  selectedTopics?: string[]; // Empty array means all topics
  difficulty: Difficulty;
  timerMinutes?: number; // Only for realExam mode
}

export interface AttemptAnswer {
  questionId: string;
  selectedAnswer: "A" | "B" | "C" | "D";
  isCorrect: boolean;
  timeSpent?: number; // Time spent on this question in seconds
  answeredAt: Date | Timestamp;
}

export interface ExamAttempt {
  id: string;
  userId: string;
  certificateId: string;
  configuration: ExamConfiguration;
  status: AttemptStatus;
  
  // Progress tracking
  currentQuestionIndex: number;
  totalQuestions: number;
  questionIds: string[]; // Ordered list of question IDs for this attempt
  
  // Answers and scoring
  answers: AttemptAnswer[];
  score?: number; // Percentage score (0-100)
  correctAnswers?: number;
  
  // Timing
  startedAt: Date | Timestamp;
  completedAt?: Date | Timestamp;
  timeSpentSeconds?: number; // Total time spent
  
  // Metadata
  createdAt?: Date | Timestamp;
  updatedAt?: Date | Timestamp;
}

export interface AttemptSummary {
  id: string;
  mode: ExamMode;
  status: AttemptStatus;
  score?: number;
  correctAnswers?: number;
  totalQuestions: number;
  startedAt: Date | Timestamp;
  completedAt?: Date | Timestamp;
  timeSpentSeconds?: number;
}

/**
 * Creates a new exam attempt for a user
 */
export async function createExamAttempt(
  userId: string,
  certificateId: string,
  configuration: ExamConfiguration,
  questionIds: string[]
): Promise<string> {
  const attemptId = `attempt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const path = `users/${userId}/examAttempts/${attemptId}`;

  const attempt: Omit<ExamAttempt, 'id' | 'createdAt' | 'updatedAt'> = {
    userId,
    certificateId,
    configuration,
    status: "inProgress",
    currentQuestionIndex: 0,
    totalQuestions: questionIds.length,
    questionIds,
    answers: [],
    startedAt: new Date(),
  };

  await createDoc(path, { id: attemptId, ...attempt });
  return attemptId;
}

/**
 * Gets all exam attempts for a user and certificate
 */
export async function getUserExamAttempts(
  userId: string,
  certificateId?: string
): Promise<ExamAttempt[]> {
  const path = `users/${userId}/examAttempts`;
  const attempts = await readCollection<ExamAttempt>(path);
  
  if (certificateId) {
    return attempts.filter(attempt => attempt.certificateId === certificateId);
  }
  
  return attempts.sort((a, b) => {
    const aTime = a.startedAt instanceof Date ? a.startedAt.getTime() : a.startedAt.toDate().getTime();
    const bTime = b.startedAt instanceof Date ? b.startedAt.getTime() : b.startedAt.toDate().getTime();
    return bTime - aTime; // Most recent first
  });
}

/**
 * Gets a specific exam attempt
 */
export async function getExamAttempt(
  userId: string,
  attemptId: string
): Promise<ExamAttempt | null> {
  const path = `users/${userId}/examAttempts/${attemptId}`;
  return await readDoc<ExamAttempt>(path);
}

/**
 * Gets the current in-progress attempt for a user and certificate
 */
export async function getCurrentAttempt(
  userId: string,
  certificateId: string
): Promise<ExamAttempt | null> {
  const attempts = await getUserExamAttempts(userId, certificateId);
  return attempts.find(attempt => attempt.status === "inProgress") || null;
}

/**
 * Updates an exam attempt
 */
export async function updateExamAttempt(
  userId: string,
  attemptId: string,
  updates: Partial<ExamAttempt>
): Promise<void> {
  const path = `users/${userId}/examAttempts/${attemptId}`;
  await updateDocFields(path, updates);
}

/**
 * Submits an answer for a question in an attempt
 */
export async function submitAnswer(
  userId: string,
  attemptId: string,
  questionId: string,
  selectedAnswer: "A" | "B" | "C" | "D",
  isCorrect: boolean,
  timeSpent?: number
): Promise<void> {
  const attempt = await getExamAttempt(userId, attemptId);
  if (!attempt) throw new Error("Attempt not found");

  const answer: AttemptAnswer = {
    questionId,
    selectedAnswer,
    isCorrect,
    timeSpent,
    answeredAt: new Date(),
  };

  const updatedAnswers = [...attempt.answers];
  const existingIndex = updatedAnswers.findIndex(a => a.questionId === questionId);
  
  if (existingIndex >= 0) {
    updatedAnswers[existingIndex] = answer;
  } else {
    updatedAnswers.push(answer);
  }

  await updateExamAttempt(userId, attemptId, {
    answers: updatedAnswers,
    currentQuestionIndex: Math.min(attempt.currentQuestionIndex + 1, attempt.totalQuestions - 1),
  });
}

/**
 * Completes an exam attempt and calculates the final score
 */
export async function completeExamAttempt(
  userId: string,
  attemptId: string
): Promise<void> {
  const attempt = await getExamAttempt(userId, attemptId);
  if (!attempt) throw new Error("Attempt not found");

  const correctAnswers = attempt.answers.filter(answer => answer.isCorrect).length;
  const score = Math.round((correctAnswers / attempt.totalQuestions) * 100);
  const startTime = attempt.startedAt instanceof Date ? attempt.startedAt.getTime() : attempt.startedAt.toDate().getTime();
  const timeSpentSeconds = Math.floor((new Date().getTime() - startTime) / 1000);

  await updateExamAttempt(userId, attemptId, {
    status: "completed",
    completedAt: new Date(),
    score,
    correctAnswers,
    timeSpentSeconds,
  });
}

/**
 * Abandons an exam attempt
 */
export async function abandonExamAttempt(
  userId: string,
  attemptId: string
): Promise<void> {
  await updateExamAttempt(userId, attemptId, {
    status: "abandoned",
  });
}

/**
 * Deletes an exam attempt
 */
export async function deleteExamAttempt(
  userId: string,
  attemptId: string
): Promise<void> {
  const path = `users/${userId}/examAttempts/${attemptId}`;
  await deleteDocByPath(path);
}

/**
 * Gets attempt summaries for display purposes
 */
export async function getAttemptSummaries(
  userId: string,
  certificateId: string
): Promise<AttemptSummary[]> {
  const attempts = await getUserExamAttempts(userId, certificateId);
  
  return attempts.map(attempt => ({
    id: attempt.id,
    mode: attempt.configuration.mode,
    status: attempt.status,
    score: attempt.score,
    correctAnswers: attempt.correctAnswers,
    totalQuestions: attempt.totalQuestions,
    startedAt: attempt.startedAt,
    completedAt: attempt.completedAt,
    timeSpentSeconds: attempt.timeSpentSeconds,
  }));
}
