import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signOut,
  updateProfile,
  signInWithPopup,
  GoogleAuthProvider,
  type User,
  type UserCredential,
} from "firebase/auth";
import { getFirebaseClients, getSecondaryAuth } from "@/app/Firebase/init";

export type Credentials = { email: string; password: string };

export async function emailPasswordSignIn({ email, password }: Credentials) {
  const { auth } = getFirebaseClients();
  const result = await signInWithEmailAndPassword(auth, email, password);
  return result.user;
}

export async function emailPasswordSignUp({ email, password }: Credentials, displayName?: string) {
  const { auth } = getFirebaseClients();
  const result = await createUserWithEmailAndPassword(auth, email, password);
  if (displayName) await updateProfile(result.user, { displayName });
  return result.user;
}

// Creates a user using a secondary Firebase app so the current session is not affected
export async function adminCreateUserWithEmailAndPassword({ email, password }: Credentials, displayName?: string) {
  const secondaryAuth = getSecondaryAuth();
  const result = await createUserWithEmailAndPassword(secondaryAuth, email, password);
  if (displayName) await updateProfile(result.user, { displayName });
  // Sign out from the secondary auth to avoid lingering sessions
  await signOut(secondaryAuth);
  return result.user;
}

export async function requestPasswordReset(email: string) {
  const { auth } = getFirebaseClients();
  await sendPasswordResetEmail(auth, email);
}

export async function googleSignIn(): Promise<UserCredential> {
  const { auth } = getFirebaseClients();
  const provider = new GoogleAuthProvider();

  // Request additional scopes for user info
  provider.addScope('profile');
  provider.addScope('email');

  const result = await signInWithPopup(auth, provider);
  return result;
}

export async function logout() {
  const { auth } = getFirebaseClients();
  await signOut(auth);
}

export type PublicUser = Pick<User, "uid" | "email" | "displayName" | "photoURL"> & { emailVerified: boolean };

export function toPublicUser(user: User | null): PublicUser | null {
  if (!user) return null;
  const { uid, email, displayName, photoURL, emailVerified } = user;
  return { uid, email: email ?? null, displayName: displayName ?? null, photoURL: photoURL ?? null, emailVerified } as PublicUser;
}


