"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Edit, Trash2, MoreHorizontal, Users } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { type UserProfile } from "@/Services/userService";
import { useTranslations } from "next-intl";

interface UserTableProps {
  users: UserProfile[];
  onEditUser: (user: UserProfile) => void;
  onDeleteUser: (user: UserProfile) => void;
  loading?: boolean;
}

export function UserTable({ users, onEditUser, onDeleteUser, loading = false }: UserTableProps) {
  const t = useTranslations('users.table');
  const [sortField, setSortField] = useState<keyof UserProfile>('displayName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const sortedUsers = [...users].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSort = (field: keyof UserProfile) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatDate = (date: UserProfile['lastLoginAt']) => {
    if (!date) return 'Never';
    // Firestore Timestamp has toDate(); Date does not
    const asAny = date as unknown as { toDate?: () => Date };
    const d = typeof asAny.toDate === 'function' ? asAny.toDate() : new Date(date as Date);
    return d.toLocaleDateString();
  };

  const getInitials = (user: UserProfile) => {
    if (user.firstName && user.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    if (user.displayName) {
      const names = user.displayName.split(' ');
      return names.length > 1 
        ? `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase()
        : names[0][0].toUpperCase();
    }
    return user.email?.[0]?.toUpperCase() || 'U';
  };

  if (loading) {
    return (
      <div className="rounded-lg bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border/60 bg-gradient-to-r from-primary to-primary-deep">
              <TableHead className="text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('user')}</TableHead>
              <TableHead className="text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('email')}</TableHead>
              <TableHead className="text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('role')}</TableHead>
              <TableHead className="text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('status')}</TableHead>
              <TableHead className="text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('lastLogin')}</TableHead>
              <TableHead className="w-[100px] text-right text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(5)].map((_, i) => (
              <TableRow key={i} className="border-b border-border/30 hover:bg-muted/30 transition-colors">
                <TableCell className="py-4">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-primary/10 rounded-full animate-pulse" />
                    <div className="space-y-1">
                      <div className="h-4 w-24 bg-primary/10 rounded animate-pulse" />
                      <div className="h-3 w-16 bg-primary/10 rounded animate-pulse" />
                    </div>
                  </div>
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-4 w-32 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-6 w-16 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-6 w-20 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-4 w-20 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-8 w-8 bg-primary/10 rounded animate-pulse" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border/60 bg-gradient-to-r from-primary to-primary-deep">
            <TableHead
              className="cursor-pointer select-none hover:text-white transition-colors text-white/90 font-display uppercase tracking-wide text-[11px] py-3"
              onClick={() => handleSort('displayName')}
            >
              {t('user')}
              {sortField === 'displayName' && (
                <span className="ml-1 text-white/90">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer select-none hover:text-white transition-colors text-white/90 font-display uppercase tracking-wide text-[11px] py-3"
              onClick={() => handleSort('email')}
            >
              {t('email')}
              {sortField === 'email' && (
                <span className="ml-1 text-white/90">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer select-none hover:text-white transition-colors text-white/90 font-display uppercase tracking-wide text-[11px] py-3"
              onClick={() => handleSort('role')}
            >
              {t('role')}
              {sortField === 'role' && (
                <span className="ml-1 text-white/90">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead className="text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('status')}</TableHead>
            <TableHead
              className="cursor-pointer select-none hover:text-white transition-colors text-white/90 font-display uppercase tracking-wide text-[11px] py-3"
              onClick={() => handleSort('lastLoginAt')}
            >
              {t('lastLogin')}
              {sortField === 'lastLoginAt' && (
                <span className="ml-1 text-white/90">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead className="w-[100px] text-right text-white/90 font-display uppercase tracking-wide text-[11px] py-3">{t('actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedUsers.map((user) => (
            <TableRow key={user.uid} className="border-b border-border/30 hover:bg-muted/30 transition-colors">
              <TableCell className="py-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10 rounded-full bg-white ring-2 ring-primary/20 ring-offset-1 ring-offset-white shadow-sm">
                    <AvatarImage src={user.photoURL || undefined} />
                    <AvatarFallback className="bg-primary/10 text-primary text-sm font-medium uppercase tracking-wide">
                      {getInitials(user)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-display text-charcoal text-[15px] leading-tight">
                      {user.displayName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'No Name'}
                    </div>
                    <div className="text-xs text-grey">
                      {user.firstName && user.lastName
                        ? `${user.firstName} ${user.lastName}`
                        : user.displayName || 'No display name'
                      }
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-grey py-4 text-sm">{user.email}</TableCell>
              <TableCell className="py-4">
                <Badge
                  variant="secondary"
                  className={
                    (user.role === 'Admin'
                      ? 'bg-primary/10 text-primary border-primary/20 '
                      : 'bg-accent/10 text-accent border-accent/20 ') +
                    'font-medium rounded-full uppercase tracking-wide text-[10px] px-2.5 py-1'
                  }
                >
                  {user.role}
                </Badge>
              </TableCell>
              <TableCell className="py-4">
                <Badge
                  variant="secondary"
                  className={
                    (user.emailVerified
                      ? 'bg-emerald-50 text-emerald-700 border-emerald-200 '
                      : 'bg-red-50 text-red-700 border-red-200 ') +
                    'font-medium rounded-full uppercase tracking-wide text-[10px] px-2.5 py-1'
                  }
                >
                  {user.emailVerified ? t('verified') : t('unverified')}
                </Badge>
              </TableCell>
              <TableCell className="text-grey py-4 text-sm">
                {formatDate(user.lastLoginAt)}
              </TableCell>
              <TableCell className="py-4 text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-primary/10 hover:text-primary transition-colors"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40 bg-white border-border/50 shadow-lg">
                    <DropdownMenuItem
                      onClick={() => onEditUser(user)}
                      className="cursor-pointer hover:bg-primary/5 focus:bg-primary/5 text-charcoal"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDeleteUser(user)}
                      className="cursor-pointer hover:bg-red-50 focus:bg-red-50 text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t('delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {users.length === 0 && !loading && (
        <div className="text-center py-12 text-grey">
          <Users className="h-12 w-12 mx-auto mb-4 text-grey/50" />
          <p className="text-lg font-medium text-charcoal mb-2">{t('emptyTitle')}</p>
          <p className="text-sm">{t('emptyDescription')}</p>
        </div>
      )}
    </div>
  );
}
