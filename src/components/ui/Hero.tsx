"use client";

import Image from "next/image";
import { cn } from "@/lib/utils";
import { dtcAssets, DtcAssetKey } from "@/lib/assets";

export interface DtcHeroProps {
  title: string;
  subtitle?: string;
  image?: DtcAssetKey;
  className?: string;
  children?: React.ReactNode;
  titleClassName?: string; // allow per-page title sizing/style overrides
}

export function DtcHero({ title, subtitle, image = "hero1", className, children, titleClassName }: DtcHeroProps) {
  const imageSrc = dtcAssets[image];

  return (
    <section
      className={cn(
        "relative isolate -mt-6 -mx-6 mb-6", // flush with the SidebarInset padding
        "min-h-[40vh] w-auto overflow-hidden",
        "grid place-items-center text-center",
        className
      )}
    >
      {/* Background image with soft mask */}
      <div className="absolute inset-0 pointer-events-none select-none">
        <Image
          src={imageSrc}
          alt="Hero"
          fill
          priority
          className="object-cover object-center opacity-100 saturate-110 scale-105"
        />
        {/* Darken slightly for contrast */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-transparent" />
      </div>

      {/* Centered content */}
      <div className="relative px-6 w-full">
        <div className="max-w-5xl mx-auto">
          <h1
            className={cn(
              "font-display font-bold text-white tracking-tight",
              "[animation:fadeInUp_360ms_ease-out]",
              titleClassName ?? "text-4xl md:text-6xl"
            )}
          >
            {title}
          </h1>
          {subtitle && (
            <p className="mt-3 text-white/85 text-base md:text-lg max-w-2xl mx-auto">
              {subtitle}
            </p>
          )}
          {children ? (
            <div className="mt-6 flex items-center justify-center">
              {children}
            </div>
          ) : null}
        </div>
      </div>

      {/* Bottom subtle divider */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
    </section>
  );
}

export default DtcHero;


