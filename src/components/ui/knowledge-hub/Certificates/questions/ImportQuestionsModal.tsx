"use client";

import { useEffect, useState } from "react";
import { DtcModal } from "@/components/ui/DtcModal";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import BrandButton from "@/components/ui/BrandButton";
import { ChevronDown, ChevronUp, CheckCircle, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Lazy import exceljs to reduce initial bundle
async function loadExcelJS() {
  const mod = await import("exceljs");
  return mod;
}

export type ParsedQuestion = {
  row: number;
  question: string;
  correct: "A" | "B" | "C" | "D";
  choices: [string, string, string, string];
};

export type ImportMode = "add" | "replace";

type ImportQuestionsModalProps = {
  open: boolean;
  onClose: () => void;
  onConfirm: (mode: ImportMode, questions: ParsedQuestion[]) => Promise<void> | void;
  importing?: boolean;
  importProgress?: { current: number; total: number };
};

export default function ImportQuestionsModal({
  open,
  onClose,
  onConfirm,
  importing = false,
  importProgress = { current: 0, total: 0 }
}: ImportQuestionsModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [parsing, setParsing] = useState(false);
  const [mode, setMode] = useState<ImportMode>("add");
  const [rows, setRows] = useState<ParsedQuestion[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 5; // Show only 5 questions per page

  useEffect(() => {
    if (!open) {
      setFile(null);
      setRows([]);
      setMode("add");
      setParsing(false);
      setCurrentPage(1);
    }
  }, [open]);

  const hasData = rows.length > 0;
  const totalPages = Math.ceil(rows.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const endIndex = startIndex + questionsPerPage;
  const currentQuestions = rows.slice(startIndex, endIndex);

  async function handleFileChange(f: File | null) {
    setFile(f);
    setRows([]);
    if (!f) return;
    try {
      setParsing(true);
      const buf = await f.arrayBuffer();
      const ExcelJS = await loadExcelJS();
      const wb = new ExcelJS.Workbook();
      await wb.xlsx.load(buf);
      const sheet = wb.worksheets[0];
      if (!sheet) {
        toast.error("No worksheet found in the Excel file");
        setParsing(false);
        return;
      }
      const parsed: ParsedQuestion[] = [];
      sheet.eachRow((row, rowNumber) => {
        const a = String(row.getCell(1).value ?? "").toString().trim();
        const b = String(row.getCell(2).value ?? "").toString().trim().toUpperCase();
        const c = String(row.getCell(3).value ?? "").toString().trim();
        const d = String(row.getCell(4).value ?? "").toString().trim();
        const e = String(row.getCell(5).value ?? "").toString().trim();
        const fCell = String(row.getCell(6).value ?? "").toString().trim();

        if (!a && !b && !c && !d && !e && !fCell) return; // skip empty rows
        if (!a) return; // skip if no question text

        const correct = (b === "A" || b === "B" || b === "C" || b === "D") ? (b as ParsedQuestion["correct"]) : "A";
        parsed.push({
          row: rowNumber,
          question: a,
          correct,
          choices: [c, d, e, fCell],
        });
      });
      setRows(parsed);
      if (parsed.length === 0) toast.message("No rows parsed. Ensure columns A-F are filled.");
    } catch (err) {
      console.error(err);
      toast.error("Failed to read Excel file");
    } finally {
      setParsing(false);
    }
  }

  // Reset to page 1 when new data is loaded
  useEffect(() => {
    if (rows.length > 0) {
      setCurrentPage(1);
    }
  }, [rows.length]);

  return (
    <DtcModal
      isOpen={open}
      onClose={importing ? () => {} : onClose}
      title="Import Questions"
      description="Upload an Excel (.xlsx) with columns: A Question, B Correct (A-D), C Choice A, D Choice B, E Choice C, F Choice D"
      size="lg"
      className="border-[--color-border]"
    >
      <div className="max-h-[70vh] overflow-y-auto grid gap-5">
        <div className="grid gap-6 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="file" className="text-charcoal font-medium">Excel File (.xlsx)</Label>
            <Input
              id="file"
              type="file"
              accept=".xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              onChange={(e) => handleFileChange(e.target.files?.[0] ?? null)}
              disabled={parsing || importing}
              className="bg-white border-muted focus:border-primary focus:ring-primary/20 file:border-0 file:bg-muted file:text-charcoal"
            />
          </div>
          <div className="space-y-3">
            <Label className="text-charcoal font-medium">Import Mode</Label>
            <RadioGroup
              className="flex gap-6"
              value={mode}
              onValueChange={(v) => !importing && setMode(v as ImportMode)}
              disabled={importing}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  id="mode-add"
                  value="add"
                  className="border-muted text-primary"
                  disabled={importing}
                />
                <Label
                  htmlFor="mode-add"
                  className={`cursor-pointer ${importing ? 'text-gray-400' : 'text-charcoal'}`}
                >
                  Add to existing
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  id="mode-replace"
                  value="replace"
                  className="border-muted text-primary"
                  disabled={importing}
                />
                <Label
                  htmlFor="mode-replace"
                  className={`cursor-pointer ${importing ? 'text-gray-400' : 'text-charcoal'}`}
                >
                  Replace current questions
                </Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-charcoal text-lg">
                {parsing ? "Parsing…" : hasData ? `${rows.length} questions imported` : "No data yet."}
              </CardTitle>
              {hasData && totalPages > 1 && (
                <div className="flex items-center gap-2">
                  <BrandButton
                    variant="ghost"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1 || importing}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </BrandButton>
                  <span className="text-sm text-grey px-2">
                    {currentPage} of {totalPages}
                  </span>
                  <BrandButton
                    variant="ghost"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages || importing}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </BrandButton>
                </div>
              )}
            </div>
          </CardHeader>
          {hasData && (
            <CardContent className="p-0">
              <div className="space-y-3 p-4">
                {currentQuestions.map((q, idx) => (
                  <CompactQuestionCard
                    key={`${q.row}-${idx}`}
                    question={q}
                    questionNumber={startIndex + idx + 1}
                  />
                ))}
              </div>
            </CardContent>
          )}
        </Card>

        {/* Import Progress Indicator */}
        {importing && (
          <Card className="mt-6 border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                  <div>
                    <h3 className="font-semibold text-blue-900">Importing Questions</h3>
                    <p className="text-sm text-blue-700">
                      Please wait while we import your questions...
                    </p>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-blue-700">
                    <span>Progress</span>
                    <span>{importProgress.current} of {importProgress.total}</span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                      style={{
                        width: importProgress.total > 0
                          ? `${(importProgress.current / importProgress.total) * 100}%`
                          : '0%'
                      }}
                    />
                  </div>
                  <div className="text-xs text-blue-600 text-center">
                    {importProgress.total > 0
                      ? `${Math.round((importProgress.current / importProgress.total) * 100)}% complete`
                      : 'Preparing...'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="mt-6 flex items-center justify-end gap-3">
          <BrandButton
            variant="outline"
            onClick={onClose}
            disabled={importing}
          >
            Cancel
          </BrandButton>
          <BrandButton
            variant="primary"
            disabled={!hasData || parsing || importing}
            onClick={async () => {
              try {
                await onConfirm(mode, rows);
                // Don't close here - let the parent handle it after import completes
              } catch (e) {
                // Let caller handle toast if needed
              }
            }}
          >
            {importing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Importing...
              </>
            ) : (
              'Continue'
            )}
          </BrandButton>
        </div>
      </div>
    </DtcModal>
  );
}

function CompactQuestionCard({ question, questionNumber }: { question: ParsedQuestion; questionNumber: number }) {
  const [showChoices, setShowChoices] = useState(false);

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
  };

  const choiceLabels = ["A", "B", "C", "D"] as const;

  return (
    <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
      <div className="flex items-start gap-3">
        {/* Question Number */}
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-white text-xs font-semibold flex-shrink-0 mt-0.5">
          Q{questionNumber}
        </div>

        {/* Question Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2 mb-2">
            <p className="text-sm text-charcoal leading-relaxed">
              {truncateText(question.question, 120)}
            </p>
            <div className="flex items-center gap-2 flex-shrink-0">
              <CheckCircle className="h-3 w-3 text-accent" />
              <span className="text-xs font-medium text-charcoal bg-accent/10 px-2 py-0.5 rounded">
                {question.correct}
              </span>
            </div>
          </div>

          {/* Toggle Choices */}
          <button
            onClick={() => setShowChoices(!showChoices)}
            className="flex items-center gap-1 text-xs text-primary hover:text-primary-deep transition-colors"
          >
            {showChoices ? (
              <>
                <ChevronUp className="h-3 w-3" />
                Hide choices
              </>
            ) : (
              <>
                <ChevronDown className="h-3 w-3" />
                Show choices
              </>
            )}
          </button>

          {/* Choices */}
          {showChoices && (
            <div className="mt-2 space-y-1">
              {question.choices.map((choice, idx) => {
                const label = choiceLabels[idx];
                const isCorrect = label === question.correct;

                return (
                  <div
                    key={idx}
                    className={`flex items-start gap-2 p-2 rounded text-xs ${
                      isCorrect
                        ? "bg-accent/10 text-charcoal"
                        : "bg-white/50 text-grey"
                    }`}
                  >
                    <span className={`font-semibold ${isCorrect ? "text-accent" : "text-grey"}`}>
                      {label}.
                    </span>
                    <span className="leading-relaxed">
                      {truncateText(choice, 80)}
                    </span>
                    {isCorrect && <CheckCircle className="h-3 w-3 text-accent mt-0.5 ml-auto flex-shrink-0" />}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}


