"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { DtcModal } from "@/components/ui/DtcModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "sonner";
import { type QuestionRecord, type QuestionInput } from "@/Services/questionsService";

interface EditQuestionModalProps {
  open: boolean;
  onClose: () => void;
  question: QuestionRecord | null;
  onSave: (questionId: string, data: Partial<QuestionInput>) => Promise<boolean>;
}

type FormData = {
  question: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correct: "A" | "B" | "C" | "D";
  rowNumber?: number;
};

export default function EditQuestionModal({
  open,
  onClose,
  question,
  onSave,
}: EditQuestionModalProps) {
  const [saving, setSaving] = useState(false);

  const form = useForm<FormData>({
    defaultValues: {
      question: "",
      choiceA: "",
      choiceB: "",
      choiceC: "",
      choiceD: "",
      correct: "A",
      rowNumber: undefined,
    },
  });

  useEffect(() => {
    if (question && open) {
      form.reset({
        question: question.question,
        choiceA: question.choices[0],
        choiceB: question.choices[1],
        choiceC: question.choices[2],
        choiceD: question.choices[3],
        correct: question.correct,
        rowNumber: question.rowNumber,
      });
    }
  }, [question, open, form]);

  const handleSave = async (data: FormData) => {
    if (!question) return;

    setSaving(true);
    try {
      const updateData: Partial<QuestionInput> = {
        question: data.question,
        choices: [data.choiceA, data.choiceB, data.choiceC, data.choiceD],
        correct: data.correct,
        rowNumber: data.rowNumber,
      };

      const success = await onSave(question.id, updateData);
      
      if (success) {
        toast.success("Question updated successfully");
        onClose();
      } else {
        toast.error("Failed to update question");
      }
    } catch (error) {
      console.error("Error updating question:", error);
      toast.error("Failed to update question");
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  if (!question) return null;

  return (
    <DtcModal
      isOpen={open}
      onClose={handleClose}
      title="Edit Question"
      description="Update the question details below"
      size="lg"
    >
      <div className="max-h-[70vh] overflow-y-auto space-y-6">

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSave)} className="space-y-6">
            <FormField
              control={form.control}
              name="rowNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-charcoal font-medium">Question Number (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="e.g., 1"
                      className="bg-white border-border focus:border-primary focus:ring-primary/20"
                      {...field}
                      value={field.value || ""}
                      onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="question"
              rules={{ required: "Question text is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-charcoal font-medium">Question Text</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter the question text..."
                      className="min-h-[100px] bg-white border-border focus:border-primary focus:ring-primary/20 text-charcoal"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600" />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-charcoal">Answer Choices</h3>
              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="choiceA"
                  rules={{ required: "Choice A is required" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-charcoal font-medium flex items-center gap-2">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-sm font-bold">A</span>
                        Choice A
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter choice A..."
                          className="bg-white border-border focus:border-primary focus:ring-primary/20 text-charcoal"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-red-600" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="choiceB"
                  rules={{ required: "Choice B is required" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-charcoal font-medium flex items-center gap-2">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-sm font-bold">B</span>
                        Choice B
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter choice B..."
                          className="bg-white border-border focus:border-primary focus:ring-primary/20 text-charcoal"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-red-600" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="choiceC"
                  rules={{ required: "Choice C is required" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-charcoal font-medium flex items-center gap-2">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-sm font-bold">C</span>
                        Choice C
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter choice C..."
                          className="bg-white border-border focus:border-primary focus:ring-primary/20 text-charcoal"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-red-600" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="choiceD"
                  rules={{ required: "Choice D is required" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-charcoal font-medium flex items-center gap-2">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-sm font-bold">D</span>
                        Choice D
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter choice D..."
                          className="bg-white border-border focus:border-primary focus:ring-primary/20 text-charcoal"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-red-600" />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="correct"
              rules={{ required: "Correct answer is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-charcoal font-medium">Correct Answer</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="bg-white border-border focus:border-primary focus:ring-primary/20 text-charcoal">
                        <SelectValue placeholder="Select the correct answer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white border-border">
                      <SelectItem value="A" className="text-charcoal hover:bg-primary/10">
                        <div className="flex items-center gap-2">
                          <span className="flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-bold">A</span>
                          Option A
                        </div>
                      </SelectItem>
                      <SelectItem value="B" className="text-charcoal hover:bg-primary/10">
                        <div className="flex items-center gap-2">
                          <span className="flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-bold">B</span>
                          Option B
                        </div>
                      </SelectItem>
                      <SelectItem value="C" className="text-charcoal hover:bg-primary/10">
                        <div className="flex items-center gap-2">
                          <span className="flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-bold">C</span>
                          Option C
                        </div>
                      </SelectItem>
                      <SelectItem value="D" className="text-charcoal hover:bg-primary/10">
                        <div className="flex items-center gap-2">
                          <span className="flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-bold">D</span>
                          Option D
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-red-600" />
                </FormItem>
              )}
            />

          </form>
        </Form>
      </div>

      {/* Footer - Fixed at bottom */}
      <div className="flex justify-end gap-3 pt-4 border-t border-border bg-white">
        <Button
          type="button"
          variant="outline"
          onClick={handleClose}
          disabled={saving}
          className="border-border text-charcoal hover:bg-muted transition-colors"
        >
          Cancel
        </Button>
        <Button
          onClick={form.handleSubmit(handleSave)}
          disabled={saving}
          className="min-w-[100px] bg-primary hover:bg-primary-deep text-white transition-all shadow-sm hover:shadow-md"
        >
          {saving ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </DtcModal>
  );
}
