"use client";

import { useEffect, useMemo, useState } from "react";
import { DtcModal } from "@/components/ui/DtcModal";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { BadgeCheck, ChevronLeft, ChevronRight, Loader2, Sparkles } from "lucide-react";
import { toast } from "sonner";
import type { QuestionRecord } from "@/Services/questionsService";

type Phase = 1 | 2 | 3 | 4 | 5;

export type CategorizeMode =
  | "topic"
  | "difficulty"
  | "topic_and_difficulty";

export interface CertificateContextInfo {
  id: string;
  name: string;
  provider: string;
  description: string;
}

type CategorizeQuestionsWizardProps = {
  isOpen: boolean;
  onClose: () => void;
  certificate: CertificateContextInfo;
  questions: QuestionRecord[];
  onCallCategorizationAI: (payload: {
    mode: CategorizeMode;
    certificate: CertificateContextInfo;
    questionItems: { id: string; question: string }[];
    selectedQuestionIds: string[];
    customGroups?: string[];
  }) => Promise<{ updates: Array<{ id: string; tags: string[]; topic?: string; difficulty?: "Easy" | "Medium" | "Hard" }>; remaining?: number; usage?: unknown }>; // API result
  onSuggestGroupsWithAI: (payload: {
    certificate: CertificateContextInfo;
    samples: { id: string; question: string }[];
  }) => Promise<{ groups: string[]; remaining?: number; usage?: unknown }>; // API result
  onPersistUpdates: (updates: Array<{ id: string; tags: string[]; topic?: string; difficulty?: "Easy" | "Medium" | "Hard" }>, onProgress: (done: number, total: number) => void) => Promise<void>;
  // taxonomy support
  initialGroups?: string[];
  onSaveGroups?: (groups: string[]) => Promise<void>;
};

export default function CategorizeQuestionsWizard(props: CategorizeQuestionsWizardProps) {
  const { isOpen, onClose, certificate, questions, onPersistUpdates, onCallCategorizationAI, onSuggestGroupsWithAI, initialGroups = [], onSaveGroups } = props;

  const [phase, setPhase] = useState<Phase>(1);
  const [mode, setMode] = useState<CategorizeMode>("topic_and_difficulty");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rlSuggestRemaining, setRlSuggestRemaining] = useState<number | undefined>(undefined);
  const [rlCategorizeRemaining, setRlCategorizeRemaining] = useState<number | undefined>(undefined);
  const [saveProgress, setSaveProgress] = useState<{ phase: "idle" | "analyzing" | "saving"; done: number; total: number }>({ phase: "idle", done: 0, total: 0 });
  const [pageIdx, setPageIdx] = useState(1);
  const [pageSize, setPageSize] = useState(100);

  // Phase 2 state (AI suggestions for groups based on certificate context)
  const [suggestedGroups, setSuggestedGroups] = useState<string[]>([]);
  const [customGroup, setCustomGroup] = useState("");
  const [decidedGroups, setDecidedGroups] = useState<string[]>(initialGroups);

  // Phase 3 state (select questions)
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  // Reset on open/close
  useEffect(() => {
    if (!isOpen) return;
    setPhase(1);
    setMode("topic_and_difficulty");
    setLoading(false);
    setError(null);
    setSuggestedGroups([]);
    setCustomGroup("");
    setSelectedIds(new Set());
    setDecidedGroups(initialGroups);
  }, [isOpen]);

  const canGoNextFromPhase1 = true;
  const canGoNextFromPhase2 = true; // groups are optional; user can proceed
  const canConfirmInPhase3 = selectedIds.size > 0;

  const toggleSelected = (id: string) => {
    setSelectedIds((prev) => {
      const next = new Set(prev);
      if (next.has(id)) next.delete(id);
      else next.add(id);
      return next;
    });
  };

  const allQuestionIds = useMemo(() => questions.map((q) => q.id), [questions]);
  const selectAll = () => setSelectedIds(new Set(allQuestionIds));
  const clearAll = () => setSelectedIds(new Set());
  const totalPages = Math.max(1, Math.ceil(questions.length / pageSize));
  const currentPageIds = useMemo(() => {
    const start = (pageIdx - 1) * pageSize;
    const end = start + pageSize;
    return questions.slice(start, end).map((q) => q.id);
  }, [questions, pageIdx, pageSize]);
  const selectCurrentPage = () => setSelectedIds(prev => {
    const next = new Set(prev);
    currentPageIds.forEach(id => next.add(id));
    return next;
  });
  const clearCurrentPage = () => setSelectedIds(prev => {
    const next = new Set(prev);
    currentPageIds.forEach(id => next.delete(id));
    return next;
  });

  async function handleSuggestGroups() {
    try {
      setLoading(true);
      setError(null);
      const samples = questions.slice(0, 8).map((q) => ({ id: q.id, question: q.question }));
      const res = await onSuggestGroupsWithAI({ certificate, samples });
      setSuggestedGroups(res.groups);
      if (typeof res.remaining === 'number') setRlSuggestRemaining(res.remaining);
      toast.success("AI suggested groups updated");
    } catch (e: any) {
      setError(e?.message ?? "Failed to get AI suggestions");
      toast.error("Failed to get AI suggestions");
    } finally {
      setLoading(false);
    }
  }

  async function handleConfirmCategorization() {
    try {
      setLoading(true);
      setError(null);
      setSaveProgress({ phase: "analyzing", done: 0, total: 0 });
      setPhase(4); // move to dedicated processing phase
      const selectedQuestionIds = Array.from(selectedIds);
      const questionItems = questions
        .filter((q) => selectedIds.has(q.id))
        .map((q) => ({ id: q.id, question: q.question }));
      const customGroups = [
        ...suggestedGroups,
        ...(
          customGroup
            .split(",")
            .map((g) => g.trim())
            .filter(Boolean)
        ),
      ];

      const result = await onCallCategorizationAI({
        mode,
        certificate,
        questionItems,
        selectedQuestionIds,
        customGroups: customGroups.length ? customGroups : undefined,
      });
      if (typeof result.remaining === 'number') setRlCategorizeRemaining(result.remaining);
      const updates = result.updates ?? [];
      setSaveProgress({ phase: "saving", done: 0, total: updates.length });
      await onPersistUpdates(updates, (done, total) => setSaveProgress({ phase: "saving", done, total }));

      toast.success("Questions categorized successfully");
      setPhase(5);
    } catch (e: any) {
      setError(e?.message ?? "Failed to categorize questions");
      toast.error("Failed to categorize questions");
    } finally {
      setLoading(false);
      setSaveProgress({ phase: "idle", done: 0, total: 0 });
    }
  }

  return (
    <DtcModal
      isOpen={isOpen}
      onClose={loading ? () => {} : onClose}
      title="Categorize Questions"
      description="Use AI to suggest groups and categorize questions by topic and/or difficulty"
      size="2xl"
      className="border-[--color-border]"
    >
      <div className="space-y-6">
        {/* Stepper */}
        <div className="flex items-center gap-3 text-sm">
          <StepBadge active={phase === 1} done={phase > 1} label="Choose Criteria" />
          <div className="h-px flex-1 bg-border" />
          <StepBadge active={phase === 2} done={phase > 2} label="Suggest Groups" />
          <div className="h-px flex-1 bg-border" />
          <StepBadge active={phase === 3} done={phase > 3} label="Select Questions" />
          <div className="h-px flex-1 bg-border" />
          <StepBadge active={phase === 4} done={phase > 4} label="Process" />
          <div className="h-px flex-1 bg-border" />
          <StepBadge active={phase === 5} done={false} label="Done" />
        </div>
        {(rlSuggestRemaining !== undefined || rlCategorizeRemaining !== undefined) && (
          <div className="text-xs text-grey">
            {rlSuggestRemaining !== undefined && <span className="mr-4">Suggest groups remaining: {rlSuggestRemaining}</span>}
            {rlCategorizeRemaining !== undefined && <span>Categorize remaining: {rlCategorizeRemaining}</span>}
          </div>
        )}

        {phase === 1 && (
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg text-charcoal">Based on what should we categorize?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <RadioGroup value={mode} onValueChange={(v) => setMode(v as CategorizeMode)} className="grid sm:grid-cols-3 gap-3">
                <RadioItem id="m1" value="topic" title="Question Topic" description="Cluster by subject area" />
                <RadioItem id="m2" value="difficulty" title="Question Difficulty" description="Easy/Medium/Hard" />
                <RadioItem id="m3" value="topic_and_difficulty" title="Topic + Difficulty" description="Both dimensions" />
              </RadioGroup>
              <div className="flex justify-end">
                <Button onClick={() => canGoNextFromPhase1 && setPhase(2)} className="bg-primary text-white">
                  Next <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {phase === 2 && (
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg text-charcoal flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-accent" /> Verify using certificate context and suggest groups
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="rounded-xl border border-border bg-muted/30 p-4">
                <div className="text-sm text-grey">Certificate</div>
                <div className="mt-1 font-medium text-charcoal">{certificate.name} — {certificate.provider}</div>
                <div className="mt-2 text-sm text-grey leading-relaxed line-clamp-4">{certificate.description}</div>
              </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-charcoal">Agreed categories</div>
              <div className="text-xs text-grey">{decidedGroups.length} total</div>
            </div>
            <div className="flex flex-wrap gap-2">
              {decidedGroups.length === 0 ? (
                <span className="text-xs text-grey">None yet</span>
              ) : decidedGroups.map((g, i) => (
                <span key={i} className="px-2 py-1 rounded-full text-xs bg-primary/5 text-primary border border-primary/30">{g}</span>
              ))}
            </div>
          </div>

              <div className="flex items-center gap-3">
                <Button variant="outline" onClick={handleSuggestGroups} disabled={loading} className="border-border">
                  {loading ? (<><Loader2 className="mr-2 h-4 w-4 animate-spin" />Thinking…</>) : (<>Ask AI for group suggestions</>)}
                </Button>
                <div className="text-xs text-grey">You can also add your own groups (comma-separated)</div>
              </div>

              <Input
                value={customGroup}
                onChange={(e) => setCustomGroup(e.target.value)}
                placeholder="e.g. Data Governance, Architecture, Integration, Security"
                className="bg-white"
              />

          <div className="space-y-1">
            <div className="text-sm font-medium text-charcoal">AI suggestions</div>
            <div className="flex flex-wrap gap-2">
              {suggestedGroups.length === 0 ? (
                <span className="text-xs text-grey">No suggestions yet</span>
              ) : suggestedGroups.map((g, i) => (
                <button
                  key={i}
                  type="button"
                  onClick={() => setDecidedGroups(Array.from(new Set([...(decidedGroups||[]), g])))}
                  className="px-2 py-1 rounded-full text-xs bg-accent/10 text-charcoal border border-accent/20 hover:bg-accent/20"
                >
                  + {g}
                </button>
              ))}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="border-border"
              onClick={() => {
                const manual = customGroup
                  .split(",")
                  .map((g) => g.trim())
                  .filter(Boolean);
                const merged = Array.from(new Set([...(decidedGroups || []), ...suggestedGroups, ...manual]));
                setDecidedGroups(merged);
              }}
            >
              Add to decided
            </Button>
            {onSaveGroups && (
              <Button className="bg-primary text-white" onClick={async () => {
                const toSave = decidedGroups.length ? decidedGroups : suggestedGroups;
                if (toSave.length === 0) return;
                await onSaveGroups(toSave);
                toast.success("Saved. Categorization will use only these categories.");
              }}>
                Save & enforce
              </Button>
            )}
          </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setPhase(1)} className="border-border">
                  <ChevronLeft className="mr-2 h-4 w-4" /> Back
                </Button>
                <Button onClick={() => canGoNextFromPhase2 && setPhase(3)} className="bg-primary text-white">
                  Next <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {phase === 3 && (
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg text-charcoal">Choose which questions to categorize</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap items-center gap-2">
                <Button variant="outline" onClick={selectAll} className="border-border text-sm">Select all</Button>
                <Button variant="outline" onClick={clearAll} className="border-border text-sm">Clear all</Button>
                <Button variant="outline" onClick={selectCurrentPage} className="border-border text-sm">Select page</Button>
                <Button variant="outline" onClick={clearCurrentPage} className="border-border text-sm">Clear page</Button>
                <div className="ml-auto flex items-center gap-2 text-xs text-grey">
                  <label className="whitespace-nowrap">Per page</label>
                  <select
                    className="rounded-md border border-border bg-white px-2 py-1 text-charcoal"
                    value={pageSize}
                    onChange={(e) => { setPageIdx(1); setPageSize(Number(e.target.value)); }}
                  >
                    <option value={60}>60</option>
                    <option value={100}>100</option>
                    <option value={150}>150</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 gap-2">
                {questions.slice((pageIdx-1)*pageSize, (pageIdx-1)*pageSize + pageSize).map((q) => {
                  const selected = selectedIds.has(q.id);
                  return (
                    <button
                      key={q.id}
                      type="button"
                      aria-pressed={selected}
                      onClick={() => toggleSelected(q.id)}
                      className={`relative h-10 rounded-md border text-xs font-semibold transition-colors flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-primary/30 ${
                        selected ? "bg-primary/10 border-primary text-primary" : "bg-white border-border text-charcoal hover:bg-muted/50"
                      }`}
                    >
                      {q.id}
                      {selected && <span className="absolute -top-1 -right-1 inline-flex h-4 w-4 items-center justify-center rounded-full bg-primary text-white text-[10px]">✓</span>}
                    </button>
                  );
                })}
              </div>
              <div className="flex items-center justify-between pt-2">
                <div className="text-xs text-grey">Page {pageIdx} of {totalPages} — Selected {selectedIds.size}</div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" disabled={pageIdx<=1} onClick={() => setPageIdx((p)=>Math.max(1,p-1))} className="border-border">Prev</Button>
                  <Button variant="outline" disabled={pageIdx>=totalPages} onClick={() => setPageIdx((p)=>Math.min(totalPages,p+1))} className="border-border">Next</Button>
                </div>
              </div>

              <div className="flex justify-between pt-2">
                <Button variant="outline" onClick={() => setPhase(2)} className="border-border">
                  <ChevronLeft className="mr-2 h-4 w-4" /> Back
                </Button>
                <Button onClick={handleConfirmCategorization} disabled={!canConfirmInPhase3 || loading} className="bg-primary text-white">
                  {loading ? (<><Loader2 className="mr-2 h-4 w-4 animate-spin" />Categorizing…</>) : (<>Confirm & Categorize</>)}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {phase === 4 && (
          <ProcessingPhase saveProgress={saveProgress} />
        )}

        {phase === 5 && (
          <div className="flex flex-col items-center justify-center py-12">
            <BadgeCheck className="h-12 w-12 text-accent mb-4" />
            <div className="text-xl font-semibold text-charcoal">Categorization Complete</div>
            <div className="text-sm text-grey mt-2">You can close this wizard now.</div>
            <div className="mt-6">
              <Button onClick={onClose} className="bg-primary text-white">Close</Button>
            </div>
          </div>
        )}

        {error && (
          <div className="text-sm text-red-600">{error}</div>
        )}
      </div>
    </DtcModal>
  );
}

function StepBadge({ active, done, label }: { active: boolean; done: boolean; label: string }) {
  return (
    <div className={`inline-flex items-center gap-2 rounded-full border px-3 py-1 ${
      active ? "border-accent text-charcoal bg-accent/10" : done ? "border-green-200 bg-green-50 text-green-700" : "border-border text-grey"
    }`}>
      <span className="h-2 w-2 rounded-full bg-current" />
      <span className="text-xs font-medium">{label}</span>
    </div>
  );
}

function RadioItem({ id, value, title, description }: { id: string; value: CategorizeMode; title: string; description: string }) {
  return (
    <label htmlFor={id} className="cursor-pointer rounded-lg border border-border bg-white p-4 hover:border-primary/50 transition-colors">
      <div className="flex items-start gap-3">
        <RadioGroupItem id={id} value={value} className="mt-0.5 border-muted text-primary" />
        <div>
          <div className="font-medium text-charcoal">{title}</div>
          <div className="text-sm text-grey">{description}</div>
        </div>
      </div>
    </label>
  );
}

function ProcessingPhase({ saveProgress }: { saveProgress: { phase: "idle"|"analyzing"|"saving"; done: number; total: number } }) {
  const isAnalyzing = saveProgress.phase === "analyzing";
  const pct = saveProgress.total > 0 ? Math.round((saveProgress.done / saveProgress.total) * 100) : 0;
  return (
    <div className="relative">
      <div className="min-h-[46vh] flex flex-col items-center justify-center">
        <div className="relative mb-6">
          <div className="h-20 w-20 rounded-full bg-gradient-to-tr from-primary/20 to-accent/20 animate-pulse" />
          <Loader2 className="absolute inset-0 m-auto h-10 w-10 animate-spin text-primary" />
        </div>
        <div className="text-xl font-semibold text-charcoal mb-1">{isAnalyzing ? "Analyzing with AI" : "Saving classifications"}</div>
        <div className="text-sm text-grey mb-6">
          {isAnalyzing ? "Please wait while we compute categories…" : `Classified ${saveProgress.done} of ${saveProgress.total}`}
        </div>
        {!isAnalyzing && (
          <div className="w-full max-w-md">
            <div className="flex justify-between text-xs text-grey mb-1">
              <span>Progress</span>
              <span>{saveProgress.done}/{saveProgress.total} ({pct}%)</span>
            </div>
            <div className="w-full h-2 rounded-full bg-muted overflow-hidden">
              <div className="h-2 rounded-full bg-primary transition-all duration-300" style={{ width: `${pct}%` }} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}


