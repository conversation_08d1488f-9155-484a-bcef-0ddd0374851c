"use client";

import { DtcModal } from "@/components/ui/DtcModal";
import { Button } from "@/components/ui/button";
import { type QuestionRecord } from "@/Services/questionsService";
import { CheckCircle } from "lucide-react";

interface QuestionPreviewModalProps {
  open: boolean;
  onClose: () => void;
  question: QuestionRecord | null;
}

export default function QuestionPreviewModal({
  open,
  onClose,
  question,
}: QuestionPreviewModalProps) {
  if (!question) return null;

  const choices = [
    { letter: "A", text: question.choices[0] },
    { letter: "B", text: question.choices[1] },
    { letter: "C", text: question.choices[2] },
    { letter: "D", text: question.choices[3] },
  ];

  return (
    <DtcModal
      isOpen={open}
      onClose={onClose}
      title="Question Preview"
      description="Review the complete question and answer choices"
      size="lg"
    >
      <div className="max-h-[70vh] overflow-y-auto space-y-6">
        {/* Question ID Badge */}
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-12 h-12 bg-primary text-white rounded-lg font-bold text-lg">
            {question.id}
          </div>
          {question.rowNumber && question.rowNumber !== parseInt(question.id.replace('Q', '')) && (
            <p className="text-sm text-grey">
              Original Row: #{question.rowNumber}
            </p>
          )}
        </div>

        {/* Question Text */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-charcoal mb-4">
              Question
            </h3>
            <div className="bg-muted rounded-xl p-6 border border-border">
              <p className="text-charcoal leading-relaxed text-base">
                {question.question}
              </p>
            </div>
          </div>

          {/* Answer Choices */}
          <div>
            <h3 className="text-lg font-semibold text-charcoal mb-4">
              Answer Options
            </h3>
            <div className="space-y-3">
              {choices.map((choice) => {
                const isCorrect = choice.letter === question.correct;
                return (
                  <div
                    key={choice.letter}
                    className={`flex items-start gap-4 p-5 rounded-xl border transition-all duration-200 ${
                      isCorrect
                        ? "bg-primary/5 border-primary/20 ring-2 ring-primary/10 shadow-sm"
                        : "bg-white border-border hover:border-primary/30"
                    }`}
                  >
                    <div
                      className={`flex items-center justify-center w-10 h-10 rounded-full text-sm font-bold ${
                        isCorrect
                          ? "bg-primary text-white"
                          : "bg-muted text-charcoal"
                      }`}
                    >
                      {choice.letter}
                    </div>
                    <div className="flex-1">
                      <p className={`text-base leading-relaxed ${isCorrect ? "text-primary font-medium" : "text-charcoal"}`}>
                        {choice.text}
                      </p>
                    </div>
                    {isCorrect && (
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-6 h-6 text-primary flex-shrink-0" />
                        <span className="text-sm font-medium text-primary">Correct</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Correct Answer Highlight */}
          <div className="bg-primary/5 border border-primary/20 rounded-xl p-5">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-primary" />
              <span className="text-base font-semibold text-primary">
                Correct Answer: Option {question.correct}
              </span>
            </div>
          </div>
        </div>


      </div>
    </DtcModal>
  );
}
