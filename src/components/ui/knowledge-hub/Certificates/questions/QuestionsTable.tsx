"use client";

import { useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { type QuestionRecord, type QuestionInput } from "@/Services/questionsService";
import { Eye, Edit, Trash2, Frown, Loader2, ChevronLeft, ChevronRight, Filter, Hash, Type, CheckCircle2, Tags } from "lucide-react";
import QuestionPreviewModal from "./QuestionPreviewModal";
import EditQuestionModal from "./EditQuestionModal";
import { toast } from "sonner";

interface QuestionsTableProps {
  questions: QuestionRecord[];
  loading: boolean;
  onEdit?: (questionId: string, data: Partial<QuestionInput>) => Promise<boolean>;
  onDelete?: (questionId: string) => Promise<boolean>;
}

export default function QuestionsTable({
  questions,
  loading,
  onEdit,
  onDelete,
}: QuestionsTableProps) {
  const [previewQuestion, setPreviewQuestion] = useState<QuestionRecord | null>(null);
  const [editQuestion, setEditQuestion] = useState<QuestionRecord | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 10;

  // Filter state
  const [idFilter, setIdFilter] = useState("");
  const [titleFilter, setTitleFilter] = useState("");
  const [correctFilter, setCorrectFilter] = useState<Set<"A"|"B"|"C"|"D">>(new Set());
  const [tagFilter, setTagFilter] = useState<Set<string>>(new Set());

  const allTags = useMemo(() => {
    const set = new Set<string>();
    for (const q of questions) {
      (q.tags ?? []).forEach(t => set.add(t));
    }
    return Array.from(set).sort((a,b) => a.localeCompare(b));
  }, [questions]);

  const handleDelete = async (questionId: string) => {
    if (!onDelete) return;

    setDeletingId(questionId);
    try {
      const success = await onDelete(questionId);
      if (success) {
        toast.success("Question deleted successfully");
      } else {
        toast.error("Failed to delete question");
      }
    } catch (error) {
      toast.error("Failed to delete question");
    } finally {
      setDeletingId(null);
    }
  };

  const truncateText = (text: string, maxLength: number = 20): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  // Filtering
  const filteredQuestions = useMemo(() => {
    const idNeedle = idFilter.trim().toLowerCase();
    const titleNeedle = titleFilter.trim().toLowerCase();
    const requireCorrect = correctFilter.size > 0;
    const requireTags = tagFilter.size > 0;
    return questions.filter(q => {
      if (idNeedle && !(q.id.toLowerCase().includes(idNeedle) || String(q.rowNumber ?? '').includes(idNeedle))) return false;
      if (titleNeedle && !q.question.toLowerCase().includes(titleNeedle)) return false;
      if (requireCorrect && !correctFilter.has(q.correct)) return false;
      if (requireTags) {
        const qtags = new Set<string>(q.tags ?? []);
        let has = false;
        for (const t of Array.from(tagFilter)) { if (qtags.has(t)) { has = true; break; } }
        if (!has) return false;
      }
      return true;
    });
  }, [questions, idFilter, titleFilter, correctFilter, tagFilter]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage) || 1;
  const startIndex = (currentPage - 1) * questionsPerPage;
  const endIndex = startIndex + questionsPerPage;
  const currentQuestions = filteredQuestions.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-16 px-8 text-center">
        <Loader2 className="w-12 h-12 text-primary animate-spin mb-4" />
        <h3 className="text-xl font-medium text-charcoal mb-2">
          Loading Questions
        </h3>
        <p className="text-grey">
          Please wait while we fetch your questions...
        </p>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 px-8 text-center">
        <Frown className="w-24 h-24 text-grey/50 mb-6" />
        <h3 className="text-2xl font-semibold text-charcoal mb-3">
          No Questions Yet
        </h3>
        <p className="text-grey text-lg mb-6 max-w-md">
          Get started by importing your first set of questions to begin building your certificate exam.
        </p>
        <div className="text-sm text-grey/70">
          Click the "Import Questions" button above to add questions
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="dtc-card rounded-xl overflow-hidden">
        <AnimatePresence initial={false}>
          {(idFilter || titleFilter || correctFilter.size > 0 || tagFilter.size > 0) && (
            <motion.div
              initial={{ opacity: 0, y: -6 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -6 }}
              className="flex items-center gap-2 px-6 py-3 border-b border-border bg-gradient-to-r from-white to-muted/40"
            >
              <span className="text-xs text-grey">Active filters:</span>
              <AnimatePresence>
                {idFilter && (
                  <motion.span layout initial={{ scale: .95, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: .95, opacity: 0 }} className="text-xs bg-white/80 backdrop-blur border border-border px-2 py-0.5 rounded shadow-sm">
                    ID includes: {idFilter}
                  </motion.span>
                )}
              </AnimatePresence>
              <AnimatePresence>
                {titleFilter && (
                  <motion.span layout initial={{ scale: .95, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: .95, opacity: 0 }} className="text-xs bg-white/80 backdrop-blur border border-border px-2 py-0.5 rounded shadow-sm">
                    Title includes: {titleFilter}
                  </motion.span>
                )}
              </AnimatePresence>
              <AnimatePresence>
                {correctFilter.size > 0 && (
                  <motion.span layout initial={{ scale: .95, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: .95, opacity: 0 }} className="text-xs bg-white/80 backdrop-blur border border-border px-2 py-0.5 rounded shadow-sm">
                    Correct: {Array.from(correctFilter).join(', ')}
                  </motion.span>
                )}
              </AnimatePresence>
              <AnimatePresence>
                {tagFilter.size > 0 && (
                  <motion.span layout initial={{ scale: .95, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: .95, opacity: 0 }} className="text-xs bg-white/80 backdrop-blur border border-border px-2 py-0.5 rounded shadow-sm">
                    Tags: {Array.from(tagFilter).join(', ')}
                  </motion.span>
                )}
              </AnimatePresence>
              <Button variant="ghost" size="sm" className="ml-auto h-7 px-2 text-xs" onClick={() => { setIdFilter(""); setTitleFilter(""); setCorrectFilter(new Set()); setTagFilter(new Set()); setCurrentPage(1); }}>Clear filters</Button>
            </motion.div>
          )}
        </AnimatePresence>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-primary border-b border-primary-deep">
              <tr>
                <HeaderWithFilter label="Question Number" Icon={Hash}>
                  <div className="space-y-3">
                    <Input value={idFilter} onChange={(e)=>{ setIdFilter(e.target.value); setCurrentPage(1); }} placeholder="Search ID or row #" className="bg-white" />
                    <div className="text-xs text-grey">Type to filter by e.g. Q12 or 12</div>
                  </div>
                </HeaderWithFilter>
                <HeaderWithFilter label="Question Title" Icon={Type}>
                  <div className="space-y-3">
                    <Input value={titleFilter} onChange={(e)=>{ setTitleFilter(e.target.value); setCurrentPage(1); }} placeholder="Contains…" className="bg-white" />
                  </div>
                </HeaderWithFilter>
                <HeaderWithFilter label="Correct Answer" Icon={CheckCircle2}>
                  <div className="grid grid-cols-2 gap-2">
                    {(["A","B","C","D"] as const).map(opt => (
                      <label key={opt} className="flex items-center gap-2 text-sm">
                        <Checkbox checked={correctFilter.has(opt)} onCheckedChange={(v)=>{
                          const next = new Set(correctFilter);
                          if (v) next.add(opt); else next.delete(opt);
                          setCorrectFilter(next); setCurrentPage(1);
                        }} />
                        {opt}
                      </label>
                    ))}
                  </div>
                </HeaderWithFilter>
                <HeaderWithFilter label="Tags" Icon={Tags}>
                  <div className="max-h-56 overflow-auto pr-1 space-y-1">
                    {allTags.length === 0 && <div className="text-xs text-grey">No tags yet</div>}
                    {allTags.map(tag => (
                      <label key={tag} className="flex items-center gap-2 text-sm">
                        <Checkbox checked={tagFilter.has(tag)} onCheckedChange={(v)=>{
                          const next = new Set(tagFilter);
                          if (v) next.add(tag); else next.delete(tag);
                          setTagFilter(next); setCurrentPage(1);
                        }} />
                        <span className="truncate max-w-[180px]" title={tag}>{tag}</span>
                      </label>
                    ))}
                  </div>
                </HeaderWithFilter>
                <th className="px-6 py-4 text-left text-sm font-semibold text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {currentQuestions.map((question, index) => (
                <tr
                  key={question.id}
                  className="hover:bg-muted/50 transition-colors"
                >
                  <td className="px-6 py-4 text-sm text-charcoal">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{question.id}</span>
                      {question.rowNumber && question.rowNumber !== parseInt(question.id.replace('Q', '')) && (
                        <span className="text-xs text-grey">({question.rowNumber})</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-charcoal">
                    <div className="max-w-xs">
                      <span title={question.question}>
                        {truncateText(question.question, 50)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-charcoal">
                    <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold">
                      {question.correct}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-charcoal">
                    <div className="flex flex-wrap gap-1 max-w-xs">
                      {question.tags && question.tags.length > 0 ? (
                        question.tags.map((tag, idx) => (
                          <span key={idx} className="px-2 py-0.5 rounded-full text-xs bg-muted text-charcoal border border-border/60">
                            {tag}
                          </span>
                        ))
                      ) : (
                        <span className="text-grey text-xs">—</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-charcoal">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setPreviewQuestion(question)}
                        className="h-8 w-8 p-0 text-primary hover:bg-primary/10"
                        title="Preview question"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {onEdit && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setEditQuestion(question)}
                          className="h-8 w-8 p-0 text-accent hover:bg-accent/10"
                          title="Edit question"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      {onDelete && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            if (confirm("Are you sure you want to delete this question?")) {
                              handleDelete(question.id);
                            }
                          }}
                          disabled={deletingId === question.id}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Delete question"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-border bg-muted/30">
            <div className="text-sm text-grey">
              Showing {startIndex + 1} to {Math.min(endIndex, questions.length)} of {questions.length} questions
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => goToPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="h-8 w-8 p-0 border-border text-charcoal hover:bg-primary/10"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* Page numbers */}
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => goToPage(pageNum)}
                      className={`h-8 w-8 p-0 text-xs ${
                        currentPage === pageNum
                          ? "bg-primary text-white border-primary"
                          : "border-border text-charcoal hover:bg-primary/10"
                      }`}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => goToPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="h-8 w-8 p-0 border-border text-charcoal hover:bg-primary/10"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      <QuestionPreviewModal
        open={!!previewQuestion}
        onClose={() => setPreviewQuestion(null)}
        question={previewQuestion}
      />

      <EditQuestionModal
        open={!!editQuestion}
        onClose={() => setEditQuestion(null)}
        question={editQuestion}
        onSave={async (questionId, data) => {
          if (onEdit) {
            const success = await onEdit(questionId, data);
            if (success) {
              setEditQuestion(null);
            }
            return success;
          }
          return false;
        }}
      />
    </>
  );
}

function HeaderWithFilter({ label, children, Icon }: { label: string; children: React.ReactNode; Icon?: React.ComponentType<{ className?: string }> }) {
  return (
    <th className="px-6 py-3 text-left text-sm font-semibold text-white align-top">
      <div className="flex items-start gap-2">
        <span className="pt-1.5 inline-flex items-center gap-2">
          {Icon && <Icon className="h-3.5 w-3.5 opacity-90" />}
          {label}
        </span>
        <Popover>
          <PopoverTrigger asChild>
            <Button size="sm" variant="ghost" className="h-7 w-7 p-0 text-white/90 hover:bg-white/10">
              <Filter className="h-3.5 w-3.5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent align="start" className="w-80 bg-white border border-border shadow-xl rounded-lg p-4">
            <motion.div initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.15 }}>
              {children}
            </motion.div>
          </PopoverContent>
        </Popover>
      </div>
    </th>
  );
}
