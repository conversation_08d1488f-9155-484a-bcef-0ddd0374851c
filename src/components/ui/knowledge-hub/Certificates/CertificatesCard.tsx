"use client";

import Link from "next/link";
import Image from "next/image";
import { useLocale } from "next-intl";
import { Card } from "@/components/ui/Card";
import { buttonVariants } from "@/components/ui/button";
import { dtcAssets } from "@/lib/assets";

export function CertificatesCard() {
  const locale = useLocale();

  return (
    <Link
      href={`/${locale}/dashboard/knowledge-hub/certificates`}
      aria-label="Open Certificates"
      className="group block rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30"
    >
      <Card className="relative aspect-square w-full overflow-hidden rounded-2xl border border-gray-100 p-0 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-md">
        <Image
          src={dtcAssets.certBackground}
          alt="Certificates background"
          fill
          className="object-cover transition-transform duration-500 ease-out group-hover:scale-[1.02]"
          sizes="(min-width: 1024px) 25vw, (min-width: 640px) 50vw, 100vw"
          priority={false}
        />
        {/* Fade layer at ~50% transparency, lighter on hover */}
        <div className="pointer-events-none absolute inset-0 z-[1] bg-black/50 transition-colors duration-300 group-hover:bg-black/30" />

        <div className="absolute inset-0 z-[2] flex flex-col items-center justify-center px-6 text-center">
          <h3 className="text-3xl font-semibold tracking-tight text-white drop-shadow-md md:text-4xl">
            Certificates
          </h3>
          <span
            className={`${buttonVariants({ size: "lg" })} mt-4 h-11 rounded-md bg-white/90 px-8 text-base text-gray-900 shadow-sm backdrop-blur-sm transition-colors hover:bg-white`}
          >
            Start
          </span>
        </div>
      </Card>
    </Link>
  );
}

export default CertificatesCard;


