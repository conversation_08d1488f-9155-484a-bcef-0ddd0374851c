"use client";

import { useLocale } from "next-intl";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { Shield } from "lucide-react";
import ConfigNavCard from "@/components/ui/knowledge-hub/Certificates/config/ConfigNavCard";
import Link from "next/link";

export default function CertificateConfiguration() {
  const { user } = useUser();
  const params = useParams();
  const locale = useLocale();
  const framework = Array.isArray(params?.framework) ? params?.framework[0] : (params?.framework as string);

  if (!user || user.role !== "Admin") {
    return (
      <div className="flex items-center gap-3 text-sm text-red-700">
        <Shield className="h-4 w-4" />
        Admin only
      </div>
    );
  }

  // No outer container box; full-width grid of nav cards
  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
      <ConfigNavCard
        href={`/${locale}/dashboard/knowledge-hub/certificates/${framework}/questions`}
        title="Questions"
        subtitle="Manage exam questions"
      />
      {/* Future cards: Banks, Scoring, Certificates, etc. */}
    </div>
  );
}


