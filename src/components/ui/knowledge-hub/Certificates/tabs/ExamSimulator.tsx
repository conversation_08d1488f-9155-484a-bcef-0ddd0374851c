"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { ExamCard, type ExamMode } from "@/components/ui/ExamAssets";
import { ExamModal } from "@/components/ui/ExamAssets/ExamModal";

export default function ExamSimulator() {
  const params = useParams();
  const [selectedMode, setSelectedMode] = useState<ExamMode | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Extract certificate ID from URL params
  const certificateId = params?.framework as string || "";

  const handleModeSelect = (mode: ExamMode) => {
    setSelectedMode(mode);
    setIsModalOpen(true);
  };

  const handleStartExam = (attemptId: string) => {
    // This is now handled by the modal itself via router.push
    console.log(`Exam started with attempt ID: ${attemptId}`);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedMode(null);
  };

  return (
    <div className="w-full">
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-charcoal mb-2">Exam Simulator</h2>
        <p className="text-grey">Choose your preferred exam mode to get started</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <ExamCard
          mode="practice"
          onModeSelect={handleModeSelect}
          className="w-full"
        />
        <ExamCard
          mode="realExam"
          onModeSelect={handleModeSelect}
          className="w-full"
        />
        <ExamCard
          mode="questionBank"
          onModeSelect={handleModeSelect}
          className="w-full"
        />
      </div>

      {/* Exam Modal */}
      {selectedMode && (
        <ExamModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          mode={selectedMode}
          certificateId={certificateId}
          onStartExam={handleStartExam}
        />
      )}
    </div>
  );
}


