"use client";

import { useState } from "react";
import { ExamCard, type ExamMode } from "@/components/ui/ExamAssets";

export default function ExamSimulator() {
  const [selectedMode, setSelectedMode] = useState<ExamMode | null>(null);

  const handleModeSelect = (mode: ExamMode) => {
    setSelectedMode(mode);
    // TODO: Navigate to the selected exam mode
    console.log(`Selected exam mode: ${mode}`);
  };

  return (
    <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-charcoal mb-2">Exam Simulator</h2>
        <p className="text-grey">Choose your preferred exam mode to get started</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <ExamCard
          mode="practice"
          onModeSelect={handleModeSelect}
          className="w-full"
        />
        <ExamCard
          mode="realExam"
          onModeSelect={handleModeSelect}
          className="w-full"
        />
        <ExamCard
          mode="questionBank"
          onModeSelect={handleModeSelect}
          className="w-full"
        />
      </div>

      {selectedMode && (
        <div className="mt-8 p-4 rounded-lg bg-muted">
          <p className="text-sm text-charcoal">
            Selected mode: <span className="font-semibold">{selectedMode}</span>
          </p>
        </div>
      )}
    </div>
  );
}


