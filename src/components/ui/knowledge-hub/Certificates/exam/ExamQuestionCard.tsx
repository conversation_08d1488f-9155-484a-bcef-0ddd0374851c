"use client";

import type { QuestionRecord } from "@/Services/questionsService";

export default function ExamQuestionCard({ question, selected, showImmediate, onSelect, disabled }: {
  question: QuestionRecord;
  selected?: "A" | "B" | "C" | "D";
  showImmediate: boolean;
  onSelect: (opt: "A" | "B" | "C" | "D") => void;
  disabled?: boolean;
}) {
  const choices: Array<{ key: "A" | "B" | "C" | "D"; text: string }> = [
    { key: "A", text: question.choices[0] },
    { key: "B", text: question.choices[1] },
    { key: "C", text: question.choices[2] },
    { key: "D", text: question.choices[3] },
  ];
  return (
    <div className="space-y-4">
      <div className="text-lg font-semibold text-charcoal">{question.question}</div>
      <div className="grid gap-3">
        {choices.map((c) => {
          const isSelected = selected === c.key;
          const isCorrectChoice = showImmediate && question.correct === c.key;
          const isWrongSelected = showImmediate && isSelected && question.correct !== c.key;
          const base = "flex items-start gap-3 rounded-md border p-3 text-left transition focus:outline-none";
          const defaultStyles = "border-border bg-white hover:bg-muted/60";
          const selectedStyles = "bg-primary/5 border-primary/40";
          const correctStyles = "border-emerald-300 bg-emerald-50 ring-emerald-300";
          const wrongStyles = "border-red-300 bg-red-50 ring-red-300";
          const disabledStyles = disabled ? "opacity-60 pointer-events-none" : "";
          const cls = `${base} ${isCorrectChoice ? correctStyles : isWrongSelected ? wrongStyles : isSelected ? selectedStyles : defaultStyles} ${disabledStyles}`;
          return (
            <button
              key={c.key}
              type="button"
              onClick={() => onSelect(c.key)}
              className={cls}
            >
              <span className={`mt-0.5 inline-flex h-6 w-6 items-center justify-center rounded-full text-sm font-bold ${isCorrectChoice ? "bg-emerald-100 text-emerald-700" : isWrongSelected ? "bg-red-100 text-red-700" : "bg-primary/10 text-primary"}`}>{c.key}</span>
              <span className="text-sm text-charcoal">{c.text}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
}


