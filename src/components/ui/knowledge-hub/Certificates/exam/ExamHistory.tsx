"use client";

import { useEffect, useState } from "react";
import { listAttempts, type ExamAttemptRecord } from "@/Services/examAttemptsService";

export default function ExamHistory({ userId, certificateId }: { userId: string; certificateId: string }) {
  const [attempts, setAttempts] = useState<ExamAttemptRecord[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        setLoading(true);
        const items = await listAttempts(userId, certificateId);
        if (!mounted) return;
        setAttempts(items);
      } finally {
        if (mounted) setLoading(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, [userId, certificateId]);

  if (loading) return <div className="text-sm text-grey">Loading history…</div>;
  if (!attempts.length) return <div className="text-sm text-grey">No attempts yet.</div>;

  return (
    <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm">
      <div className="mb-3 text-sm font-semibold text-charcoal">Your Attempts</div>
      <div className="grid gap-2">
        {attempts.map((a) => (
          <div key={a.id} className="flex items-center justify-between rounded-lg border border-border bg-white px-3 py-2 text-sm">
            <div className="flex flex-col">
              <span className="font-medium text-charcoal">{a.mode} • {new Date(a.startedAt).toLocaleString()}</span>
              <span className="text-xs text-grey">{a.status}{a.score ? ` • ${a.score.correct}/${a.score.total} (${a.score.percentage}%)` : ""}</span>
            </div>
            <div className="text-xs text-grey">{a.settings.totalQuestions} Q{a.settings.timeLimitSec ? ` • ${Math.round((a.settings.timeLimitSec||0)/60)} min` : ""}</div>
          </div>
        ))}
      </div>
    </div>
  );
}



