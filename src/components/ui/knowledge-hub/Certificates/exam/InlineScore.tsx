"use client";

import type { ExamAttemptRecord } from "@/Services/examAttemptsService";

export default function InlineScore({ questions }: { questions: ExamAttemptRecord["questions"] }) {
  const answered = questions.filter((q) => typeof q.isCorrect === "boolean");
  const correct = answered.filter((q) => q.isCorrect).length;
  const total = questions.length;
  const pct = total > 0 ? Math.round((correct / total) * 100) : 0;
  return (
    <div className="rounded-md border border-border bg-gradient-to-r from-primary/5 to-accent/5 p-3 text-sm text-charcoal">
      <span className="font-medium">Progress:</span> {answered.length}/{total} <span className="mx-2">•</span>
      <span className="font-medium">Correct:</span> {correct} <span className="mx-2">•</span>
      <span className="font-medium">{pct}%</span>
    </div>
  );
}


