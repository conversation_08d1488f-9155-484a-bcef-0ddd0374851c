"use client";

import * as React from "react";
import Link from "next/link";
import { useLocale } from "next-intl";
import { type CertificateRecord, deleteCertificate } from "@/Services/certificateDetails";
import { dtcAssets } from "@/lib/assets";
import { useUser } from "@/hooks/useUser";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Pencil, Trash2 } from "lucide-react";

type CertificateCardProps = {
  certificate: CertificateRecord;
  className?: string;
};

export default function CertificateCard({ certificate, className }: CertificateCardProps) {
  const { user } = useUser();
  const locale = useLocale();
  const backgroundImageUrl = certificate.backgroundUrl || dtcAssets.certBackground;
  const slug = React.useMemo(() => {
    return certificate.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }, [certificate.name]);

  return (
    <Link
      href={`/${locale}/dashboard/knowledge-hub/certificates/${slug}`}
      className="group block focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30"
    >
      <div
        className={
          "group relative overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-md " +
          (className ?? "")
        }
        aria-label={`${certificate.name} certificate`}
        role="region"
      >
        <div className="relative aspect-[11/8.5] w-full">
          {/* Background image */}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img src={backgroundImageUrl} alt="" aria-hidden="true" className="absolute inset-0 h-full w-full object-cover" />

          {/* Dim overlay: hidden by default, appears lightly on hover */}
          <div className="absolute inset-0 bg-black opacity-0 transition-opacity duration-300 ease-out group-hover:opacity-60" />

          {/* Outer and inner frames */}
          <div className="pointer-events-none absolute inset-0 rounded-2xl ring-1 ring-gray-100" />
          <div className="pointer-events-none absolute inset-3 rounded-xl border border-gray-200/80 shadow-inner" />

          {/* Text overlay: hidden by default, appears on hover */}
          <div className="absolute inset-0 z-[1] flex flex-col items-center justify-center px-8 text-center opacity-0 transition-opacity duration-300 ease-out group-hover:opacity-100">
            <div className="text-xs font-medium uppercase tracking-wider text-white/80">{certificate.provider}</div>
            <div className="mt-1 font-serif text-3xl font-semibold leading-tight text-white md:text-4xl">
              {certificate.name}
            </div>
          </div>

          {/* Admin actions */}
          {user?.role === "Admin" && (
            <div className="absolute right-3 top-3 z-[2] flex gap-2">
              <Button
                size="icon"
                variant="secondary"
                className="h-9 w-9 rounded-full bg-white/90 text-gray-900 shadow-sm hover:bg-white"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const event = new CustomEvent("certificate:edit", { detail: { id: certificate.id } });
                  window.dispatchEvent(event);
                }}
                aria-label="Edit certificate"
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size="icon"
                    variant="secondary"
                    className="h-9 w-9 rounded-full bg-white/90 text-gray-900 shadow-sm hover:bg-white"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    aria-label="Delete certificate"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="bg-white border border-gray-200">
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete certificate?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete this certificate.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async () => {
                        try {
                          await deleteCertificate(certificate.id);
                          const evt = new CustomEvent("certificate:deleted", { detail: { id: certificate.id } });
                          window.dispatchEvent(evt);
                        } catch {
                          // swallow
                        }
                      }}
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}


