"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";

type ConfigNavCardProps = {
  href: string;
  title: string;
  subtitle?: string;
  backgroundUrl?: string;
  icon?: React.ReactNode;
  className?: string;
};

export default function ConfigNavCard({ href, title, subtitle, backgroundUrl, icon, className }: ConfigNavCardProps) {
  const bg = backgroundUrl || dtcAssets.certBackground;

  return (
    <Link
      href={href}
      className={cn("group block focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30", className)}
    >
      <div
        className={
          "relative overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-md"
        }
      >
        <div className="relative aspect-[11/8.5] w-full">
          {/* Background image */}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img src={bg} alt="" aria-hidden="true" className="absolute inset-0 h-full w-full object-cover" />

          {/* Dim overlay: hidden by default, appears on hover */}
          <div className="absolute inset-0 bg-black opacity-0 transition-opacity duration-300 ease-out group-hover:opacity-60" />

          {/* Frames */}
          <div className="pointer-events-none absolute inset-0 rounded-2xl ring-1 ring-gray-100" />
          <div className="pointer-events-none absolute inset-3 rounded-xl border border-gray-200/80 shadow-inner" />

          {/* Text overlay: hidden by default, appears on hover */}
          <div className="absolute inset-0 z-[1] flex flex-col items-center justify-center px-8 text-center opacity-0 transition-opacity duration-300 ease-out group-hover:opacity-100">
            {icon ? <div className="mb-2 text-white/85">{icon}</div> : null}
            <div className="font-serif text-2xl font-semibold leading-tight text-white md:text-3xl">{title}</div>
            {subtitle ? (
              <div className="mt-1 text-sm text-white/90">{subtitle}</div>
            ) : null}
          </div>
        </div>
      </div>
    </Link>
  );
}


