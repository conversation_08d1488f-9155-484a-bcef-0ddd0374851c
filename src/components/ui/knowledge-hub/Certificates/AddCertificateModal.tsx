"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { DtcModal } from "@/components/ui/DtcModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "sonner";
import { createCertificateDetails, type CertificateDomain, type CertificateQuestionType, type CertificateRecord, updateCertificate } from "@/Services/certificateDetails";

type AddCertificateFormValues = {
  name: string;
  provider: string;
  description: string;
  backgroundUrl: string;
  questionType: CertificateQuestionType;
  domain: CertificateDomain;
};

interface AddCertificateModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentUserUid: string;
  onCreated?: (id: string) => void;
  editCertificate?: CertificateRecord | null;
  onUpdated?: (id: string) => void;
}

export default function AddCertificateModal({ isOpen, onClose, currentUserUid, onCreated, editCertificate, onUpdated }: AddCertificateModalProps) {
  const [submitting, setSubmitting] = useState(false);
  const [imageOk, setImageOk] = useState(true);
  const form = useForm<AddCertificateFormValues>({
    defaultValues: {
      name: "",
      provider: "",
      description: "",
      backgroundUrl: "",
      questionType: "multiple_choice",
      domain: "data_management",
    },
    mode: "onSubmit",
  });

  const watchedBackgroundUrl = form.watch("backgroundUrl");

  useEffect(() => {
    // reset preview error state when URL changes
    setImageOk(true);
  }, [watchedBackgroundUrl]);

  useEffect(() => {
    if (editCertificate) {
      form.reset({
        name: editCertificate.name,
        provider: editCertificate.provider,
        description: editCertificate.description,
        backgroundUrl: editCertificate.backgroundUrl,
        questionType: editCertificate.questionType,
        domain: editCertificate.domain,
      });
    } else if (isOpen) {
      // ensure clean form when switching back to create mode
      form.reset({
        name: "",
        provider: "",
        description: "",
        backgroundUrl: "",
        questionType: "multiple_choice",
        domain: "data_management",
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editCertificate, isOpen]);

  const isValidUrl = (value: string) => {
    try {
      // eslint-disable-next-line no-new
      new URL(value);
      return true;
    } catch {
      return false;
    }
  };

  async function onSubmit(values: AddCertificateFormValues) {
    try {
      setSubmitting(true);
      // Basic URL validation
      const valid = isValidUrl(values.backgroundUrl);
      if (!valid) {
        toast.error("Please provide a valid Background URL");
        setSubmitting(false);
        return;
      }

      if (editCertificate) {
        await updateCertificate(editCertificate.id, { ...values });
        toast.success("Certificate updated successfully");
        if (onUpdated) onUpdated(editCertificate.id);
        onClose();
      } else {
        const id = await createCertificateDetails({ ...values, createdByUid: currentUserUid });
        toast.success("Certificate created successfully");
        if (onCreated) onCreated(id);
        form.reset();
        onClose();
      }
    } catch (e) {
      console.error(e);
      toast.error(editCertificate ? "Failed to update certificate" : "Failed to create certificate");
    } finally {
      setSubmitting(false);
    }
  }

  return (
      <DtcModal
      isOpen={isOpen}
      onClose={onClose}
      title={editCertificate ? "Edit Certificate" : "Add Certificate"}
      description={editCertificate ? "Update certificate details" : "Create a new certificate"}
      size="xl"
      className="border border-gray-200"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            rules={{ required: "Certificate name is required" }}
            render={({ field }) => (
              <FormItem className="sm:col-span-1">
                <FormLabel>Certificate Name</FormLabel>
                <FormControl>
                  <Input className="bg-white border border-gray-200" placeholder="e.g., TOGAF 10" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="provider"
            rules={{ required: "Provider is required" }}
            render={({ field }) => (
              <FormItem className="sm:col-span-1">
                <FormLabel>Certificate Provider</FormLabel>
                <FormControl>
                  <Input className="bg-white border border-gray-200" placeholder="e.g., The Open Group" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="backgroundUrl"
            rules={{ required: "Background URL is required" }}
            render={({ field }) => (
              <FormItem className="sm:col-span-2">
                <FormLabel>Certificate Background URL</FormLabel>
                <FormControl>
                  <Input className="bg-white border border-gray-200" placeholder="https://…" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {watchedBackgroundUrl && isValidUrl(watchedBackgroundUrl) && imageOk && (
            <div className="sm:col-span-2">
              <div className="mt-1 rounded-md border border-gray-200 bg-white p-2">
                <div className="mb-2 text-xs text-gray-500">Preview</div>
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={watchedBackgroundUrl}
                  alt="Background preview"
                  className="h-40 w-full rounded object-cover"
                  onError={() => setImageOk(false)}
                />
              </div>
            </div>
          )}

          <FormField
            control={form.control}
            name="description"
            rules={{ required: "Description is required" }}
            render={({ field }) => (
              <FormItem className="sm:col-span-2">
                <FormLabel>Certificate Description</FormLabel>
                <FormControl>
                  <Textarea className="bg-white border border-gray-200" rows={4} placeholder="Brief description" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="questionType"
            rules={{ required: "Question type is required" }}
            render={({ field }) => (
              <FormItem className="sm:col-span-1">
                <FormLabel>Certificate Question Types</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className="w-full bg-white border border-gray-200">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-white border border-gray-200">
                    <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                    <SelectItem value="graded_multiple_choice">Graded Multiple Choice</SelectItem>
                    <SelectItem value="text_input">Text Input</SelectItem>
                    <SelectItem value="mix">Mix</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="domain"
            rules={{ required: "Domain is required" }}
            render={({ field }) => (
              <FormItem className="sm:col-span-1">
                <FormLabel>Domain</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className="w-full bg-white border border-gray-200">
                      <SelectValue placeholder="Select domain" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-white border border-gray-200">
                    <SelectItem value="data_management">Data Management</SelectItem>
                    <SelectItem value="enterprise_architecture">Enterprise Architecture</SelectItem>
                    <SelectItem value="it_governance">IT Governance</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="sm:col-span-2 mt-2 flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
            <Button type="submit" disabled={submitting}>{submitting ? "Saving…" : (editCertificate ? "Update Certificate" : "Save Certificate")}</Button>
          </div>
        </form>
      </Form>
    </DtcModal>
  );
}


