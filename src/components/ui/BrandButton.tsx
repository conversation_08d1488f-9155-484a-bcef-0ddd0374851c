"use client";
import type { ButtonHTMLAttributes, ReactNode } from "react";

type Props = ButtonHTMLAttributes<HTMLButtonElement> & {
  variant?: "primary" | "outline" | "ghost";
  asChild?: boolean;
  children?: ReactNode;
};

export default function BrandButton({ variant = "primary", className = "", asChild = false, children, ...props }: Props) {
  const base = "inline-flex items-center justify-center rounded-xl transition-colors px-5 py-3 text-sm font-semibold";
  const styles = {
    primary: "bg-primary text-white hover:bg-primary-deep",
    outline: "border border-border text-charcoal hover:bg-muted",
    ghost: "text-primary hover:bg-muted",
  }[variant];

  if (asChild && children) {
    return (
      <span className={`${base} ${styles} ${className}`} role="button">
        {children}
      </span>
    );
  }
  return (
    <button className={`${base} ${styles} ${className}`} {...props}>
      {children}
    </button>
  );
}


