"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { AlertTriangle, CheckCircle, Clock, X } from "lucide-react";

interface ExamCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  unansweredQuestions: number[];
  totalQuestions: number;
  onReviewUnanswered: () => void;
  onSubmitAnyway: () => void;
  isSubmitting: boolean;
}

export function ExamCompletionModal({
  isOpen,
  onClose,
  unansweredQuestions,
  totalQuestions,
  onReviewUnanswered,
  onSubmitAnyway,
  isSubmitting,
}: ExamCompletionModalProps) {
  const hasUnanswered = unansweredQuestions.length > 0;
  const answeredCount = totalQuestions - unansweredQuestions.length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0 bg-white shadow-2xl rounded-2xl overflow-hidden">
        <DialogTitle className="sr-only">
          Exam Completion Review
        </DialogTitle>
        {/* Header */}
        <div className={`px-8 py-6 ${hasUnanswered ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-gradient-to-r from-green-500 to-green-600'} text-white relative`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white/20 rounded-xl p-3">
                {hasUnanswered ? (
                  <AlertTriangle className="h-6 w-6" />
                ) : (
                  <CheckCircle className="h-6 w-6" />
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold">
                  {hasUnanswered ? "Exam Completion Review" : "Ready to Submit"}
                </h2>
                <p className="text-white/80">
                  {hasUnanswered 
                    ? "Please review your answers before submitting" 
                    : "All questions have been answered"}
                </p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="icon"
              className="h-10 w-10 text-white/80 hover:text-white hover:bg-white/10 rounded-xl"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="p-8">
          {/* Progress Summary */}
          <div className="grid grid-cols-2 gap-6 mb-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="bg-green-100 rounded-lg p-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-700">{answeredCount}</p>
                  <p className="text-sm text-green-600">Questions Answered</p>
                </div>
              </div>
            </div>

            {hasUnanswered && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-yellow-100 rounded-lg p-2">
                    <Clock className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-700">{unansweredQuestions.length}</p>
                    <p className="text-sm text-yellow-600">Questions Remaining</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-charcoal">Progress</span>
              <span className="text-sm text-grey">{answeredCount} of {totalQuestions}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-500 ${
                  hasUnanswered ? 'bg-gradient-to-r from-yellow-400 to-orange-400' : 'bg-gradient-to-r from-green-400 to-green-500'
                }`}
                style={{ width: `${(answeredCount / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Unanswered Questions List */}
          {hasUnanswered && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-charcoal mb-4">Unanswered Questions</h3>
              <div className="bg-gray-50 rounded-lg p-4 max-h-32 overflow-y-auto">
                <div className="flex flex-wrap gap-2">
                  {unansweredQuestions.map((questionNum) => (
                    <span
                      key={questionNum}
                      className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-md text-sm font-medium"
                    >
                      Question {questionNum + 1}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Warning Message */}
          {hasUnanswered && (
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5" />
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    <strong>Important:</strong> Unanswered questions will be marked as incorrect. 
                    We recommend reviewing and answering all questions before submitting.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4">
            {hasUnanswered ? (
              <>
                <Button
                  onClick={onReviewUnanswered}
                  className="flex-1 bg-primary hover:bg-primary-deep text-white h-12 rounded-lg font-medium"
                >
                  Review Unanswered Questions
                </Button>
                <Button
                  onClick={onSubmitAnyway}
                  variant="outline"
                  disabled={isSubmitting}
                  className="h-12 px-6 rounded-lg font-medium border-yellow-300 text-yellow-700 hover:bg-yellow-50"
                >
                  {isSubmitting ? "Submitting..." : "Submit Anyway"}
                </Button>
              </>
            ) : (
              <Button
                onClick={onSubmitAnyway}
                disabled={isSubmitting}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white h-12 rounded-lg font-medium"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/20 border-t-white"></div>
                    Submitting Exam...
                  </div>
                ) : (
                  "Submit Exam"
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
