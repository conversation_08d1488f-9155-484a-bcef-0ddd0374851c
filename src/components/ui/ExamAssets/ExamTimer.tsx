"use client";

import { useState, useEffect, useImperativeHandle, forwardRef } from "react";
import { Clock, Pause, Play } from "lucide-react";
import { type ExamMode } from "@/Services/examAttemptsService";

interface ExamTimerProps {
  mode: ExamMode;
  timerMinutes?: number;
  isPaused: boolean;
  onTimeUp: () => void;
}

export interface ExamTimerRef {
  pause: () => void;
  resume: () => void;
}

const ExamTimer = forwardRef<ExamTimerRef, ExamTimerProps>(({ 
  mode, 
  timerMinutes, 
  isPaused, 
  onTimeUp 
}, ref) => {
  const [elapsedSeconds, setElapsedSeconds] = useState(0);
  const [remainingSeconds, setRemainingSeconds] = useState(
    mode === "realExam" && timerMinutes ? timerMinutes * 60 : 0
  );
  const [isRunning, setIsRunning] = useState(true);

  useImperativeHandle(ref, () => ({
    pause: () => setIsRunning(false),
    resume: () => setIsRunning(true),
  }));

  useEffect(() => {
    if (!isRunning || isPaused) return;

    const interval = setInterval(() => {
      setElapsedSeconds(prev => prev + 1);
      
      if (mode === "realExam" && timerMinutes) {
        setRemainingSeconds(prev => {
          if (prev <= 1) {
            onTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, isPaused, mode, timerMinutes, onTimeUp]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    if (mode === "realExam" && timerMinutes) {
      const percentRemaining = (remainingSeconds / (timerMinutes * 60)) * 100;
      if (percentRemaining <= 10) return "text-red-600";
      if (percentRemaining <= 25) return "text-yellow-600";
      return "text-green-600";
    }
    return "text-primary";
  };

  const getTimerBgColor = () => {
    if (mode === "realExam" && timerMinutes) {
      const percentRemaining = (remainingSeconds / (timerMinutes * 60)) * 100;
      if (percentRemaining <= 10) return "bg-red-50 border-red-200";
      if (percentRemaining <= 25) return "bg-yellow-50 border-yellow-200";
      return "bg-green-50 border-green-200";
    }
    return "bg-primary/5 border-primary/20";
  };

  return (
    <div className={`flex items-center gap-3 px-4 py-2 rounded-lg border ${getTimerBgColor()}`}>
      <div className="flex items-center gap-2">
        {isPaused ? (
          <Pause className="h-4 w-4 text-yellow-600" />
        ) : (
          <Clock className={`h-4 w-4 ${getTimerColor()}`} />
        )}
        
        <div className="text-sm font-medium">
          {mode === "realExam" && timerMinutes ? (
            <div className="flex flex-col">
              <span className={`font-mono ${getTimerColor()}`}>
                {formatTime(remainingSeconds)}
              </span>
              <span className="text-xs text-grey">remaining</span>
            </div>
          ) : (
            <div className="flex flex-col">
              <span className={`font-mono ${getTimerColor()}`}>
                {formatTime(elapsedSeconds)}
              </span>
              <span className="text-xs text-grey">elapsed</span>
            </div>
          )}
        </div>
      </div>

      {isPaused && (
        <div className="flex items-center gap-1 text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded">
          <Pause className="h-3 w-3" />
          Paused
        </div>
      )}
    </div>
  );
});

ExamTimer.displayName = "ExamTimer";

export default ExamTimer;
