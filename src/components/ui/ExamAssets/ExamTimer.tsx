"use client";

import { useState, useEffect, useImperativeHandle, forwardRef } from "react";
import { Clock, Pause, Play } from "lucide-react";
import { type ExamMode } from "@/Services/examAttemptsService";

interface ExamTimerProps {
  mode: ExamMode;
  timerMinutes?: number;
  isPaused: boolean;
  onTimeUp: () => void;
}

export interface ExamTimerRef {
  pause: () => void;
  resume: () => void;
}

const ExamTimer = forwardRef<ExamTimerRef, ExamTimerProps>(({ 
  mode, 
  timerMinutes, 
  isPaused, 
  onTimeUp 
}, ref) => {
  const [elapsedSeconds, setElapsedSeconds] = useState(0);
  const [remainingSeconds, setRemainingSeconds] = useState(
    mode === "realExam" && timerMinutes ? timerMinutes * 60 : 0
  );
  const [isRunning, setIsRunning] = useState(true);

  useImperativeHandle(ref, () => ({
    pause: () => setIsRunning(false),
    resume: () => setIsRunning(true),
  }));

  useEffect(() => {
    if (!isRunning || isPaused) return;

    const interval = setInterval(() => {
      setElapsedSeconds(prev => prev + 1);
      
      if (mode === "realExam" && timerMinutes) {
        setRemainingSeconds(prev => {
          if (prev <= 1) {
            onTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, isPaused, mode, timerMinutes, onTimeUp]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    if (mode === "realExam" && timerMinutes) {
      const percentRemaining = (remainingSeconds / (timerMinutes * 60)) * 100;
      if (percentRemaining <= 10) return "text-red-200";
      if (percentRemaining <= 25) return "text-yellow-200";
      return "text-green-200";
    }
    return "text-white";
  };

  const getTimerBgColor = () => {
    if (mode === "realExam" && timerMinutes) {
      const percentRemaining = (remainingSeconds / (timerMinutes * 60)) * 100;
      if (percentRemaining <= 10) return "bg-red-500/20 border-red-400";
      if (percentRemaining <= 25) return "bg-yellow-500/20 border-yellow-400";
      return "bg-green-500/20 border-green-400";
    }
    return "bg-white/10 border-white/20";
  };

  return (
    <div className={`flex items-center gap-4 px-6 py-3 rounded-xl border ${getTimerBgColor()} backdrop-blur-sm`}>
      <div className="flex items-center gap-3">
        {isPaused ? (
          <div className="bg-yellow-500/20 rounded-lg p-2">
            <Pause className="h-5 w-5 text-yellow-200" />
          </div>
        ) : (
          <div className="bg-white/10 rounded-lg p-2">
            <Clock className={`h-5 w-5 ${getTimerColor()}`} />
          </div>
        )}

        <div className="text-left">
          <div className={`text-2xl font-mono font-bold ${getTimerColor()}`}>
            {mode === "realExam" && timerMinutes ? formatTime(remainingSeconds) : formatTime(elapsedSeconds)}
          </div>
          <div className="text-xs text-white/70 uppercase tracking-wide">
            {mode === "realExam" && timerMinutes ? "Time Remaining" : "Time Elapsed"}
          </div>
        </div>
      </div>

      {isPaused && (
        <div className="flex items-center gap-2 text-sm text-yellow-200 bg-yellow-500/20 px-3 py-1.5 rounded-lg border border-yellow-400/30">
          <Pause className="h-4 w-4" />
          <span className="font-medium">Paused</span>
        </div>
      )}
    </div>
  );
});

ExamTimer.displayName = "ExamTimer";

export default ExamTimer;
