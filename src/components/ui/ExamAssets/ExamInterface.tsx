"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useUser } from "@/hooks/useUser";
import {
  type ExamAttempt,
  updateExamAttempt,
  submitAnswer,
  completeExamAttempt
} from "@/Services/examAttemptsService";
import { type QuestionRecord } from "@/Services/questionsService";
import { DtcHero } from "@/components/ui/Hero";
import ExamTimer from "./ExamTimer";
import ExamNavigation from "./ExamNavigation";
import QuestionDisplay from "./QuestionDisplay";
import { ExamCompletionModal } from "./ExamCompletionModal";
import { Button } from "@/components/ui/button";
import { Flag, ArrowLeft, ArrowRight, CheckCircle } from "lucide-react";

interface ExamInterfaceProps {
  attempt: ExamAttempt;
  questions: QuestionRecord[];
  onComplete: () => void;
  onExit: () => void;
  onAttemptUpdate: (attempt: ExamAttempt) => void;
  heroTitle: string;
}

export default function ExamInterface({
  attempt,
  questions,
  onComplete,
  onExit,
  onAttemptUpdate,
  heroTitle
}: ExamInterfaceProps) {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(attempt.currentQuestionIndex || 0);
  const [selectedAnswer, setSelectedAnswer] = useState<"A" | "B" | "C" | "D" | null>(null);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());
  const [isTimerPaused, setIsTimerPaused] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const timerRef = useRef<{ pause: () => void; resume: () => void } | null>(null);

  const currentQuestion = questions[currentQuestionIndex];
  const isRealExamMode = attempt.configuration.mode === "realExam";
  const totalQuestions = questions.length;

  // Check if current question has been answered
  const currentAnswer = attempt.answers.find(
    answer => answer.questionId === currentQuestion?.id
  );

  useEffect(() => {
    // Set selected answer if question was already answered
    if (currentAnswer) {
      setSelectedAnswer(currentAnswer.selectedAnswer);
    } else {
      setSelectedAnswer(null);
    }
    setShowConfirmation(false);
  }, [currentQuestionIndex, currentAnswer]);

  // Handle visibility change (pause timer when tab is not active)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        setIsTimerPaused(true);
        timerRef.current?.pause();
      } else {
        setIsTimerPaused(false);
        timerRef.current?.resume();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, []);

  const handleAnswerSelect = (answer: "A" | "B" | "C" | "D") => {
    setSelectedAnswer(answer);
    setShowConfirmation(true);
  };

  const handleConfirmAnswer = async () => {
    if (!selectedAnswer || !currentQuestion || !user) return;

    try {
      const isCorrect = selectedAnswer === currentQuestion.correct;

      await submitAnswer(
        user.uid,
        attempt.id,
        currentQuestion.id,
        selectedAnswer,
        isCorrect
      );

      // Update local attempt state
      const updatedAnswers = [
        ...attempt.answers.filter(a => a.questionId !== currentQuestion.id),
        {
          questionId: currentQuestion.id,
          selectedAnswer,
          isCorrect,
          answeredAt: new Date(),
        }
      ];

      const updatedAttempt = {
        ...attempt,
        answers: updatedAnswers,
        currentQuestionIndex: Math.min(currentQuestionIndex + 1, totalQuestions - 1)
      };

      onAttemptUpdate(updatedAttempt);
      setShowConfirmation(false);

      // Auto-advance to next question if not the last one
      if (currentQuestionIndex < totalQuestions - 1) {
        setTimeout(() => {
          setCurrentQuestionIndex(currentQuestionIndex + 1);
        }, 500);
      }
    } catch (error) {
      console.error("Error submitting answer:", error);
    }
  };

  const handleQuestionNavigation = (questionIndex: number) => {
    setCurrentQuestionIndex(questionIndex);
  };

  const handleFlagQuestion = () => {
    const newFlagged = new Set(flaggedQuestions);
    if (newFlagged.has(currentQuestionIndex)) {
      newFlagged.delete(currentQuestionIndex);
    } else {
      newFlagged.add(currentQuestionIndex);
    }
    setFlaggedQuestions(newFlagged);
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const getUnansweredQuestions = () => {
    const answeredQuestionIds = new Set(attempt.answers.map(answer => answer.questionId));
    return questions
      .map((question, index) => ({ question, index }))
      .filter(({ question }) => !answeredQuestionIds.has(question.id))
      .map(({ index }) => index);
  };

  const handleCompleteExam = () => {
    setShowCompletionModal(true);
  };

  const handleReviewUnanswered = () => {
    const unansweredQuestions = getUnansweredQuestions();
    if (unansweredQuestions.length > 0) {
      setCurrentQuestionIndex(unansweredQuestions[0]);
      setShowCompletionModal(false);
    }
  };

  const handleSubmitExam = async () => {
    if (!user) return;

    try {
      setIsSubmitting(true);
      await completeExamAttempt(user.uid, attempt.id);

      // Navigate to performance report
      router.push(`/${locale}/dashboard/knowledge-hub/certificates/${attempt.certificateId}/exam/${attempt.id}/report`);
    } catch (error) {
      console.error("Error completing exam:", error);
      setIsSubmitting(false);
    }
  };

  const getQuestionStatus = (questionIndex: number) => {
    const question = questions[questionIndex];
    const answer = attempt.answers.find(a => a.questionId === question?.id);
    
    if (flaggedQuestions.has(questionIndex)) return "flagged";
    if (!answer) return "unanswered";
    if (isRealExamMode) return "answered";
    return answer.isCorrect ? "correct" : "incorrect";
  };

  if (!currentQuestion) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-red-600">Question not found</p>
          <Button onClick={onExit} className="mt-4">
            Exit Exam
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-muted/30">
      {/* Hero with organized exam controls */}
      <DtcHero
        title={heroTitle}
        subtitle={`Question ${currentQuestionIndex + 1} of ${totalQuestions}`}
        image="hero2"
        className="pb-12"
      >
        <div className="w-full max-w-6xl mx-auto space-y-8">
          {/* Top Row: Timer and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ExamTimer
                ref={timerRef}
                mode={attempt.configuration.mode}
                timerMinutes={attempt.configuration.timerMinutes}
                isPaused={isTimerPaused}
                onTimeUp={handleCompleteExam}
              />
            </div>

            <div className="flex items-center gap-3">
              <Button
                onClick={handleFlagQuestion}
                variant="outline"
                size="sm"
                className={`${
                  flaggedQuestions.has(currentQuestionIndex)
                    ? "bg-yellow-500/20 border-yellow-400 text-yellow-200 hover:bg-yellow-500/30"
                    : "bg-white/10 border-white/20 text-white hover:bg-white/20"
                } transition-all duration-200`}
              >
                <Flag className="h-4 w-4 mr-2" />
                {flaggedQuestions.has(currentQuestionIndex) ? "Flagged" : "Flag Question"}
              </Button>

              <Button
                onClick={onExit}
                variant="outline"
                size="sm"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20 transition-all duration-200"
              >
                Exit Exam
              </Button>
            </div>
          </div>

          {/* Bottom Row: Question Navigation */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="mb-3">
              <h3 className="text-white/90 text-sm font-medium text-center">Question Navigation</h3>
            </div>
            <ExamNavigation
              currentQuestionIndex={currentQuestionIndex}
              totalQuestions={totalQuestions}
              onQuestionSelect={handleQuestionNavigation}
              getQuestionStatus={getQuestionStatus}
            />
          </div>
        </div>
      </DtcHero>

      <div className="container mx-auto px-4 py-8">
        {/* Question Display */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8 mb-6">
          <QuestionDisplay
            question={currentQuestion}
            selectedAnswer={selectedAnswer}
            onAnswerSelect={handleAnswerSelect}
            showCorrectAnswer={!isRealExamMode && !!currentAnswer}
            isAnswered={!!currentAnswer}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
            variant="outline"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex items-center gap-3">
            {showConfirmation && selectedAnswer && (
              <Button
                onClick={handleConfirmAnswer}
                className="bg-primary hover:bg-primary-deep text-white flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Confirm Answer
              </Button>
            )}

            {currentQuestionIndex === totalQuestions - 1 ? (
              <Button
                onClick={handleCompleteExam}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Complete Exam
              </Button>
            ) : (
              <Button
                onClick={handleNextQuestion}
                disabled={currentQuestionIndex === totalQuestions - 1}
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Exam Completion Modal */}
      <ExamCompletionModal
        isOpen={showCompletionModal}
        onClose={() => setShowCompletionModal(false)}
        unansweredQuestions={getUnansweredQuestions()}
        totalQuestions={totalQuestions}
        onReviewUnanswered={handleReviewUnanswered}
        onSubmitAnyway={handleSubmitExam}
        isSubmitting={isSubmitting}
      />
    </div>
  );
}
