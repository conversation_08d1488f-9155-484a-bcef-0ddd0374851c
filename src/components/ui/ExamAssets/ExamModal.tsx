"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { DtcModal } from "@/components/ui/DtcModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { dtcAssets } from "@/lib/assets";
import { useUser } from "@/hooks/useUser";
import { 
  type ExamMode, 
  type ExamConfiguration, 
  type AttemptSummary,
  getCurrentAttempt,
  getAttemptSummaries,
  createExamAttempt,
  abandonExamAttempt
} from "@/Services/examAttemptsService";
import { listQuestions, type QuestionRecord } from "@/Services/questionsService";
import { Clock, Trophy, Target, AlertTriangle, Play, Settings, History } from "lucide-react";

interface ExamModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: ExamMode;
  certificateId: string;
  onStartExam: (attemptId: string) => void;
}

const modeConfig = {
  practice: {
    title: "Practice Mode",
    icon: Target,
    color: "text-accent",
    bgColor: "bg-accent/10",
  },
  realExam: {
    title: "Real Exam Mode",
    icon: Clock,
    color: "text-red-500",
    bgColor: "bg-red-50",
  },
  questionBank: {
    title: "Question Bank",
    icon: Trophy,
    color: "text-primary",
    bgColor: "bg-primary/10",
  },
};

export function ExamModal({ isOpen, onClose, mode, certificateId, onStartExam }: ExamModalProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [currentAttempt, setCurrentAttempt] = useState<any>(null);
  const [previousAttempts, setPreviousAttempts] = useState<AttemptSummary[]>([]);
  const [availableQuestions, setAvailableQuestions] = useState<QuestionRecord[]>([]);
  const [availableTopics, setAvailableTopics] = useState<string[]>([]);
  
  // Configuration state
  const [configuration, setConfiguration] = useState<ExamConfiguration>({
    mode,
    numberOfQuestions: 10,
    selectedTopics: [],
    difficulty: "All",
    timerMinutes: 60,
  });

  const config = modeConfig[mode];
  const IconComponent = config.icon;

  useEffect(() => {
    if (isOpen && user) {
      loadData();
    }
  }, [isOpen, user, certificateId]);

  const loadData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Load current attempt
      const current = await getCurrentAttempt(user.uid, certificateId);
      setCurrentAttempt(current);

      // Load previous attempts
      const summaries = await getAttemptSummaries(user.uid, certificateId);
      setPreviousAttempts(summaries.filter(s => s.status === "completed"));

      // Load available questions
      const questions = await listQuestions(certificateId);
      setAvailableQuestions(questions);

      // Extract unique topics
      const topics = [...new Set(questions.map(q => q.topic).filter(Boolean))];
      setAvailableTopics(topics);

      // Set default number of questions
      if (mode !== "questionBank") {
        setConfiguration(prev => ({
          ...prev,
          numberOfQuestions: Math.min(10, questions.length),
        }));
      }
    } catch (error) {
      console.error("Error loading exam data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartNewAttempt = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Filter questions based on configuration
      let filteredQuestions = availableQuestions;

      // Filter by topics
      if (configuration.selectedTopics && configuration.selectedTopics.length > 0) {
        filteredQuestions = filteredQuestions.filter(q => 
          configuration.selectedTopics!.includes(q.topic || "")
        );
      }

      // Filter by difficulty
      if (configuration.difficulty !== "All") {
        filteredQuestions = filteredQuestions.filter(q => q.difficulty === configuration.difficulty);
      }

      // Limit number of questions (except for question bank mode)
      if (mode !== "questionBank" && configuration.numberOfQuestions) {
        // Shuffle and take the specified number
        const shuffled = [...filteredQuestions].sort(() => Math.random() - 0.5);
        filteredQuestions = shuffled.slice(0, configuration.numberOfQuestions);
      }

      if (filteredQuestions.length === 0) {
        alert("No questions match your criteria. Please adjust your settings.");
        return;
      }

      const questionIds = filteredQuestions.map(q => q.id);
      const attemptId = await createExamAttempt(user.uid, certificateId, configuration, questionIds);
      
      onStartExam(attemptId);
      onClose();
    } catch (error) {
      console.error("Error creating exam attempt:", error);
      alert("Failed to start exam. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleContinueAttempt = () => {
    if (currentAttempt) {
      onStartExam(currentAttempt.id);
      onClose();
    }
  };

  const handleAbandonAttempt = async () => {
    if (!user || !currentAttempt) return;

    setLoading(true);
    try {
      await abandonExamAttempt(user.uid, currentAttempt.id);
      setCurrentAttempt(null);
    } catch (error) {
      console.error("Error abandoning attempt:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatDate = (date: any) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <DtcModal
      isOpen={isOpen}
      onClose={onClose}
      title=""
      size="2xl"
      className="max-w-4xl"
      headerClassName="bg-gradient-to-r from-primary to-primary-deep text-white"
    >
      {/* Custom Header with DTC Logo */}
      <div className="bg-gradient-to-r from-primary to-primary-deep text-white px-6 py-6 -mx-6 -mt-6 mb-6">
        <div className="flex items-center gap-4">
          <Image
            src={dtcAssets.logoBlack}
            alt="DTC Logo"
            width={48}
            height={48}
            className="bg-white rounded-lg p-2"
          />
          <div>
            <h2 className="text-2xl font-semibold">{config.title}</h2>
            <p className="text-white/80">Configure your exam settings</p>
          </div>
          <div className={`ml-auto p-3 rounded-full ${config.bgColor}`}>
            <IconComponent className={`h-6 w-6 ${config.color}`} />
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-grey">Loading exam data...</p>
          </div>
        </div>
      ) : currentAttempt ? (
        // Current attempt in progress
        <div className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-yellow-800">Exam in Progress</h3>
                <p className="text-yellow-700 text-sm mt-1">
                  You have an ongoing {config.title.toLowerCase()} attempt. You can continue where you left off or abandon it to start fresh.
                </p>
                <div className="mt-3 text-sm text-yellow-600">
                  <p>Progress: {currentAttempt.answers?.length || 0} of {currentAttempt.totalQuestions} questions answered</p>
                  <p>Started: {formatDate(currentAttempt.startedAt)}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={handleContinueAttempt}
              className="flex-1 bg-primary hover:bg-primary-deep text-white"
            >
              <Play className="h-4 w-4 mr-2" />
              Continue Exam
            </Button>
            <Button
              onClick={handleAbandonAttempt}
              variant="outline"
              className="border-red-200 text-red-600 hover:bg-red-50"
              disabled={loading}
            >
              Abandon & Start New
            </Button>
          </div>
        </div>
      ) : (
        // Configuration and previous attempts
        <div className="space-y-8">
          {/* Previous Attempts */}
          {previousAttempts.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-charcoal mb-4 flex items-center gap-2">
                <History className="h-5 w-5" />
                Previous Attempts
              </h3>
              <div className="grid gap-3 max-h-40 overflow-y-auto">
                {previousAttempts.slice(0, 5).map((attempt) => (
                  <div key={attempt.id} className="bg-muted rounded-lg p-3 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-full ${config.bgColor}`}>
                        <IconComponent className={`h-4 w-4 ${config.color}`} />
                      </div>
                      <div>
                        <p className="font-medium text-charcoal">
                          {attempt.score}% ({attempt.correctAnswers}/{attempt.totalQuestions})
                        </p>
                        <p className="text-sm text-grey">
                          {formatDate(attempt.startedAt)} • {attempt.timeSpentSeconds ? formatDuration(attempt.timeSpentSeconds) : 'N/A'}
                        </p>
                      </div>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      attempt.score && attempt.score >= 70 
                        ? 'bg-green-100 text-green-700' 
                        : 'bg-red-100 text-red-700'
                    }`}>
                      {attempt.score && attempt.score >= 70 ? 'Passed' : 'Failed'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Configuration */}
          {mode !== "questionBank" && (
            <div>
              <h3 className="text-lg font-semibold text-charcoal mb-4 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Exam Configuration
              </h3>
              <div className="grid gap-6">
                {/* Number of Questions */}
                <div className="space-y-2">
                  <Label htmlFor="numQuestions">Number of Questions</Label>
                  <Input
                    id="numQuestions"
                    type="number"
                    min="1"
                    max={availableQuestions.length}
                    value={configuration.numberOfQuestions || ""}
                    onChange={(e) => setConfiguration(prev => ({
                      ...prev,
                      numberOfQuestions: parseInt(e.target.value) || 1
                    }))}
                    className="w-full"
                  />
                  <p className="text-sm text-grey">Maximum: {availableQuestions.length} questions available</p>
                </div>

                {/* Timer (Real Exam Mode only) */}
                {mode === "realExam" && (
                  <div className="space-y-2">
                    <Label htmlFor="timer">Timer (minutes)</Label>
                    <Input
                      id="timer"
                      type="number"
                      min="1"
                      value={configuration.timerMinutes || ""}
                      onChange={(e) => setConfiguration(prev => ({
                        ...prev,
                        timerMinutes: parseInt(e.target.value) || 60
                      }))}
                      className="w-full"
                    />
                  </div>
                )}

                {/* Difficulty */}
                <div className="space-y-2">
                  <Label>Difficulty Level</Label>
                  <Select
                    value={configuration.difficulty}
                    onValueChange={(value: any) => setConfiguration(prev => ({
                      ...prev,
                      difficulty: value
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Difficulties</SelectItem>
                      <SelectItem value="Easy">Easy</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="Hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Topics */}
                {availableTopics.length > 0 && (
                  <div className="space-y-2">
                    <Label>Question Topics</Label>
                    <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                      {availableTopics.map((topic) => (
                        <div key={topic} className="flex items-center space-x-2">
                          <Checkbox
                            id={topic}
                            checked={configuration.selectedTopics?.includes(topic)}
                            onCheckedChange={(checked) => {
                              setConfiguration(prev => ({
                                ...prev,
                                selectedTopics: checked
                                  ? [...(prev.selectedTopics || []), topic]
                                  : (prev.selectedTopics || []).filter(t => t !== topic)
                              }));
                            }}
                          />
                          <Label htmlFor={topic} className="text-sm">{topic}</Label>
                        </div>
                      ))}
                    </div>
                    <p className="text-sm text-grey">
                      {configuration.selectedTopics?.length === 0 ? "All topics selected" : `${configuration.selectedTopics?.length} topics selected`}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Start Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleStartNewAttempt}
              disabled={loading}
              className="bg-primary hover:bg-primary-deep text-white px-8"
            >
              {loading ? "Starting..." : `Start ${config.title}`}
            </Button>
          </div>
        </div>
      )}
    </DtcModal>
  );
}
