"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { dtcAssets } from "@/lib/assets";
import { useUser } from "@/hooks/useUser";
import {
  type ExamMode,
  type ExamConfiguration,
  type AttemptSummary,
  getCurrentAttempt,
  getAttemptSummaries,
  createExamAttempt,
  abandonExamAttempt
} from "@/Services/examAttemptsService";
import { listQuestions, type QuestionRecord } from "@/Services/questionsService";
import { Clock, Trophy, Target, AlertTriangle, Play, Settings, History, X, CheckCircle2 } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface ExamModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: ExamMode;
  certificateId: string;
  onStartExam: (attemptId: string) => void;
}

const modeConfig = {
  practice: {
    title: "Practice Mode",
    icon: Target,
    color: "text-accent",
    bgColor: "bg-accent/10",
  },
  realExam: {
    title: "Real Exam Mode",
    icon: Clock,
    color: "text-red-500",
    bgColor: "bg-red-50",
  },
  questionBank: {
    title: "Question Bank",
    icon: Trophy,
    color: "text-primary",
    bgColor: "bg-primary/10",
  },
};

export function ExamModal({ isOpen, onClose, mode, certificateId, onStartExam }: ExamModalProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [currentAttempt, setCurrentAttempt] = useState<any>(null);
  const [previousAttempts, setPreviousAttempts] = useState<AttemptSummary[]>([]);
  const [availableQuestions, setAvailableQuestions] = useState<QuestionRecord[]>([]);
  const [availableTopics, setAvailableTopics] = useState<string[]>([]);
  
  // Configuration state
  const [configuration, setConfiguration] = useState<ExamConfiguration>({
    mode,
    numberOfQuestions: 10,
    selectedTopics: [],
    difficulty: "All",
    timerMinutes: 60,
  });

  const config = modeConfig[mode];
  const IconComponent = config.icon;

  useEffect(() => {
    if (isOpen && user) {
      loadData();
    }
  }, [isOpen, user, certificateId]);

  const loadData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Load current attempt
      const current = await getCurrentAttempt(user.uid, certificateId);
      setCurrentAttempt(current);

      // Load previous attempts
      const summaries = await getAttemptSummaries(user.uid, certificateId);
      setPreviousAttempts(summaries.filter(s => s.status === "completed"));

      // Load available questions
      const questions = await listQuestions(certificateId);
      setAvailableQuestions(questions);

      // Extract unique topics
      const topics = [...new Set(questions.map(q => q.topic).filter(Boolean))];
      setAvailableTopics(topics);

      // Set default number of questions
      if (mode !== "questionBank") {
        setConfiguration(prev => ({
          ...prev,
          numberOfQuestions: Math.min(10, questions.length),
        }));
      }
    } catch (error) {
      console.error("Error loading exam data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartNewAttempt = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Filter questions based on configuration
      let filteredQuestions = availableQuestions;

      // Filter by topics
      if (configuration.selectedTopics && configuration.selectedTopics.length > 0) {
        filteredQuestions = filteredQuestions.filter(q => 
          configuration.selectedTopics!.includes(q.topic || "")
        );
      }

      // Filter by difficulty
      if (configuration.difficulty !== "All") {
        filteredQuestions = filteredQuestions.filter(q => q.difficulty === configuration.difficulty);
      }

      // Limit number of questions (except for question bank mode)
      if (mode !== "questionBank" && configuration.numberOfQuestions) {
        // Shuffle and take the specified number
        const shuffled = [...filteredQuestions].sort(() => Math.random() - 0.5);
        filteredQuestions = shuffled.slice(0, configuration.numberOfQuestions);
      }

      if (filteredQuestions.length === 0) {
        alert("No questions match your criteria. Please adjust your settings.");
        return;
      }

      const questionIds = filteredQuestions.map(q => q.id);
      const attemptId = await createExamAttempt(user.uid, certificateId, configuration, questionIds);
      
      onStartExam(attemptId);
      onClose();
    } catch (error) {
      console.error("Error creating exam attempt:", error);
      alert("Failed to start exam. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleContinueAttempt = () => {
    if (currentAttempt) {
      onStartExam(currentAttempt.id);
      onClose();
    }
  };

  const handleAbandonAttempt = async () => {
    if (!user || !currentAttempt) return;

    setLoading(true);
    try {
      await abandonExamAttempt(user.uid, currentAttempt.id);
      setCurrentAttempt(null);
    } catch (error) {
      console.error("Error abandoning attempt:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatDate = (date: any) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl p-0 bg-white shadow-2xl rounded-2xl overflow-hidden">
        {/* Custom Header with DTC Logo */}
        <div className="bg-gradient-to-r from-primary to-primary-deep text-white px-8 py-6 relative">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white rounded-xl p-3 shadow-lg">
                <Image
                  src={dtcAssets.logoBlack}
                  alt="DTC Logo"
                  width={32}
                  height={32}
                  className="object-contain"
                />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">{config.title}</h2>
                <p className="text-white/80 text-sm">Configure your exam settings</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-xl ${config.bgColor} backdrop-blur-sm`}>
                <IconComponent className={`h-6 w-6 ${config.color}`} />
              </div>
              <Button
                onClick={onClose}
                variant="ghost"
                size="icon"
                className="h-10 w-10 text-white/80 hover:text-white hover:bg-white/10 rounded-xl"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        <div className="p-8">{/* Content will go here */}

          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
                <p className="text-grey text-lg">Loading exam data...</p>
              </div>
            </div>
          ) : currentAttempt ? (
            // Current attempt in progress
            <div className="space-y-8">
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 shadow-sm">
                <div className="flex items-start gap-4">
                  <div className="bg-yellow-100 rounded-xl p-3">
                    <AlertTriangle className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-charcoal mb-2">Exam in Progress</h3>
                    <p className="text-grey mb-4">
                      You have an ongoing {config.title.toLowerCase()} attempt. You can continue where you left off or abandon it to start fresh.
                    </p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="bg-white rounded-xl p-4 shadow-sm">
                        <p className="text-grey mb-1">Progress</p>
                        <p className="text-charcoal font-semibold">
                          {currentAttempt.answers?.length || 0} of {currentAttempt.totalQuestions} questions
                        </p>
                      </div>
                      <div className="bg-white rounded-xl p-4 shadow-sm">
                        <p className="text-grey mb-1">Started</p>
                        <p className="text-charcoal font-semibold">{formatDate(currentAttempt.startedAt)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <Button
                  onClick={handleContinueAttempt}
                  className="flex-1 bg-primary hover:bg-primary-deep text-white h-12 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
                >
                  <Play className="h-5 w-5 mr-2" />
                  Continue Exam
                </Button>
                <Button
                  onClick={handleAbandonAttempt}
                  variant="outline"
                  className="h-12 px-6 rounded-xl font-semibold text-red-600 hover:bg-red-50 hover:text-red-700 transition-all"
                  disabled={loading}
                >
                  Abandon & Start New
                </Button>
              </div>
            </div>
          ) : (
            // Configuration and previous attempts
            <div className="space-y-8">
              {/* Previous Attempts */}
              {previousAttempts.length > 0 && (
                <div>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="bg-primary/10 rounded-xl p-3">
                      <History className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="text-xl font-bold text-charcoal">Previous Attempts</h3>
                  </div>
                  <div className="grid gap-4 max-h-48 overflow-y-auto pr-2">
                    {previousAttempts.slice(0, 5).map((attempt) => (
                      <div key={attempt.id} className="bg-gradient-to-r from-white to-muted/30 rounded-2xl p-5 shadow-sm hover:shadow-md transition-all">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-xl ${config.bgColor} shadow-sm`}>
                              <IconComponent className={`h-5 w-5 ${config.color}`} />
                            </div>
                            <div>
                              <p className="text-lg font-bold text-charcoal">
                                {attempt.score}% ({attempt.correctAnswers}/{attempt.totalQuestions})
                              </p>
                              <p className="text-grey">
                                {formatDate(attempt.startedAt)} • {attempt.timeSpentSeconds ? formatDuration(attempt.timeSpentSeconds) : 'N/A'}
                              </p>
                            </div>
                          </div>
                          <div className={`px-4 py-2 rounded-xl text-sm font-bold shadow-sm ${
                            attempt.score && attempt.score >= 70
                              ? 'bg-accent text-white'
                              : 'bg-red-500 text-white'
                          }`}>
                            {attempt.score && attempt.score >= 70 ? 'Passed' : 'Failed'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Configuration */}
              {mode !== "questionBank" && (
                <div>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="bg-primary/10 rounded-xl p-3">
                      <Settings className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="text-xl font-bold text-charcoal">Exam Configuration</h3>
                  </div>
                  <div className="grid gap-8">
                    {/* Number of Questions */}
                    <div className="space-y-3">
                      <label className="text-sm font-semibold text-charcoal">Number of Questions</label>
                      <div className="relative">
                        <input
                          type="number"
                          min="1"
                          max={availableQuestions.length}
                          value={configuration.numberOfQuestions || ""}
                          onChange={(e) => setConfiguration(prev => ({
                            ...prev,
                            numberOfQuestions: parseInt(e.target.value) || 1
                          }))}
                          className="w-full h-12 px-4 bg-white rounded-xl shadow-sm text-charcoal font-medium focus:outline-none focus:ring-2 focus:ring-primary/20 focus:shadow-lg transition-all"
                          placeholder="Enter number of questions"
                        />
                      </div>
                      <p className="text-sm text-grey">Maximum: {availableQuestions.length} questions available</p>
                    </div>

                    {/* Timer (Real Exam Mode only) */}
                    {mode === "realExam" && (
                      <div className="space-y-3">
                        <label className="text-sm font-semibold text-charcoal">Timer (minutes)</label>
                        <div className="relative">
                          <Clock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-grey" />
                          <input
                            type="number"
                            min="1"
                            value={configuration.timerMinutes || ""}
                            onChange={(e) => setConfiguration(prev => ({
                              ...prev,
                              timerMinutes: parseInt(e.target.value) || 60
                            }))}
                            className="w-full h-12 pl-12 pr-4 bg-white rounded-xl shadow-sm text-charcoal font-medium focus:outline-none focus:ring-2 focus:ring-primary/20 focus:shadow-lg transition-all"
                            placeholder="Enter timer duration"
                          />
                        </div>
                      </div>
                    )}

                    {/* Difficulty */}
                    <div className="space-y-3">
                      <label className="text-sm font-semibold text-charcoal">Difficulty Level</label>
                      <div className="grid grid-cols-4 gap-3">
                        {["All", "Easy", "Medium", "Hard"].map((diff) => (
                          <button
                            key={diff}
                            onClick={() => setConfiguration(prev => ({ ...prev, difficulty: diff as any }))}
                            className={`h-12 rounded-xl font-semibold transition-all ${
                              configuration.difficulty === diff
                                ? 'bg-primary text-white shadow-lg'
                                : 'bg-white text-grey hover:bg-primary/5 hover:text-primary shadow-sm'
                            }`}
                          >
                            {diff}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Topics */}
                    {availableTopics.length > 0 && (
                      <div className="space-y-3">
                        <label className="text-sm font-semibold text-charcoal">Question Topics</label>
                        <div className="grid grid-cols-2 gap-3 max-h-40 overflow-y-auto pr-2">
                          {availableTopics.map((topic) => (
                            <div
                              key={topic}
                              onClick={() => {
                                setConfiguration(prev => ({
                                  ...prev,
                                  selectedTopics: prev.selectedTopics?.includes(topic)
                                    ? prev.selectedTopics.filter(t => t !== topic)
                                    : [...(prev.selectedTopics || []), topic]
                                }));
                              }}
                              className={`p-4 rounded-xl cursor-pointer transition-all ${
                                configuration.selectedTopics?.includes(topic)
                                  ? 'bg-primary text-white shadow-lg'
                                  : 'bg-white text-grey hover:bg-primary/5 hover:text-primary shadow-sm'
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <div className={`w-5 h-5 rounded-full flex items-center justify-center ${
                                  configuration.selectedTopics?.includes(topic)
                                    ? 'bg-white/20'
                                    : 'bg-grey/20'
                                }`}>
                                  {configuration.selectedTopics?.includes(topic) && (
                                    <CheckCircle2 className="w-3 h-3" />
                                  )}
                                </div>
                                <span className="font-medium text-sm">{topic}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                        <p className="text-sm text-grey">
                          {configuration.selectedTopics?.length === 0 ? "All topics selected" : `${configuration.selectedTopics?.length} topics selected`}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Start Button */}
              <div className="flex justify-center pt-4">
                <Button
                  onClick={handleStartNewAttempt}
                  disabled={loading}
                  className="bg-gradient-to-r from-primary to-primary-deep hover:from-primary-deep hover:to-primary text-white px-12 h-14 rounded-xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
                >
                  {loading ? (
                    <div className="flex items-center gap-3">
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white/20 border-t-white"></div>
                      Starting...
                    </div>
                  ) : (
                    <div className="flex items-center gap-3">
                      <Play className="h-5 w-5" />
                      Start {config.title}
                    </div>
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
