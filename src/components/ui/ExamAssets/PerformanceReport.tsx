"use client";

import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { type ExamAttempt } from "@/Services/examAttemptsService";
import { type QuestionRecord } from "@/Services/questionsService";
import { SimpleChart } from "./SimpleChart";
import {
  Trophy,
  Target,
  Clock,
  TrendingUp,
  BarChart3,
  Download,
  RefreshCw,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Flag,
  PieChart,
  BookOpen
} from "lucide-react";

interface PerformanceReportProps {
  attempt: ExamAttempt;
  questions: QuestionRecord[];
  previousAttempts?: any[]; // Previous attempt summaries
  onRetakeExam: () => void;
  onBackToCertificate: () => void;
}

interface PerformanceAnalytics {
  overallScore: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalQuestions: number;
  averageTimePerQuestion: number;
  difficultyPerformance: {
    Easy: { correct: number; total: number; avgTime: number };
    Medium: { correct: number; total: number; avgTime: number };
    Hard: { correct: number; total: number; avgTime: number };
  };
  categoryPerformance: Record<string, { correct: number; total: number }>;
  strongestCategory: string;
  weakestCategory: string;
  examMomentum: { questionIndex: number; isCorrect: boolean; timeSpent: number }[];
}

export function PerformanceReport({
  attempt,
  questions,
  previousAttempts = [],
  onRetakeExam,
  onBackToCertificate
}: PerformanceReportProps) {
  const [hoveredQuestion, setHoveredQuestion] = useState<number | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'correct' | 'incorrect' | 'unanswered'>('all');

  const filteredQuestions = useMemo(() => {
    return questions.filter((question, index) => {
      const answer = attempt.answers.find(a => a.questionId === question.id);
      const isAnswered = !!answer;
      const isCorrect = answer?.isCorrect || false;

      switch (selectedFilter) {
        case 'correct':
          return isAnswered && isCorrect;
        case 'incorrect':
          return isAnswered && !isCorrect;
        case 'unanswered':
          return !isAnswered;
        default:
          return true;
      }
    });
  }, [questions, attempt.answers, selectedFilter]);

  const analytics: PerformanceAnalytics = useMemo(() => {
    const correctAnswers = attempt.correctAnswers || 0;
    const totalQuestions = attempt.totalQuestions;
    const overallScore = attempt.score || 0;
    
    // Calculate average time per question
    const totalTimeSpent = attempt.timeSpentSeconds || 0;
    const averageTimePerQuestion = totalTimeSpent / totalQuestions;

    // Initialize difficulty performance
    const difficultyPerformance = {
      Easy: { correct: 0, total: 0, avgTime: 0 },
      Medium: { correct: 0, total: 0, avgTime: 0 },
      Hard: { correct: 0, total: 0, avgTime: 0 },
    };

    // Initialize category performance
    const categoryPerformance: Record<string, { correct: number; total: number }> = {};

    // Process each question and answer
    const examMomentum: { questionIndex: number; isCorrect: boolean; timeSpent: number }[] = [];
    
    questions.forEach((question, index) => {
      const answer = attempt.answers.find(a => a.questionId === question.id);
      const isCorrect = answer?.isCorrect || false;
      const timeSpent = answer?.timeSpent || 0;

      // Difficulty analysis
      const difficulty = question.difficulty as keyof typeof difficultyPerformance;
      if (difficulty && difficultyPerformance[difficulty]) {
        difficultyPerformance[difficulty].total++;
        if (isCorrect) difficultyPerformance[difficulty].correct++;
        if (timeSpent > 0) {
          difficultyPerformance[difficulty].avgTime += timeSpent;
        }
      }

      // Category analysis
      const category = question.topic || "General";
      if (!categoryPerformance[category]) {
        categoryPerformance[category] = { correct: 0, total: 0 };
      }
      categoryPerformance[category].total++;
      if (isCorrect) categoryPerformance[category].correct++;

      // Momentum tracking - only include answered questions with valid time
      if (answer && timeSpent > 0) {
        examMomentum.push({
          questionIndex: index,
          isCorrect,
          timeSpent,
        });
      }
    });

    // Calculate average times for difficulties
    Object.keys(difficultyPerformance).forEach(key => {
      const diff = difficultyPerformance[key as keyof typeof difficultyPerformance];
      if (diff.total > 0) {
        diff.avgTime = diff.avgTime / diff.total;
      }
    });

    // Find strongest and weakest categories
    let strongestCategory = "";
    let weakestCategory = "";
    let highestAccuracy = 0;
    let lowestAccuracy = 1;

    Object.entries(categoryPerformance).forEach(([category, performance]) => {
      const accuracy = performance.correct / performance.total;
      if (accuracy > highestAccuracy) {
        highestAccuracy = accuracy;
        strongestCategory = category;
      }
      if (accuracy < lowestAccuracy) {
        lowestAccuracy = accuracy;
        weakestCategory = category;
      }
    });

    return {
      overallScore,
      correctAnswers,
      incorrectAnswers: totalQuestions - correctAnswers,
      totalQuestions,
      averageTimePerQuestion,
      difficultyPerformance,
      categoryPerformance,
      strongestCategory,
      weakestCategory,
      examMomentum,
    };
  }, [attempt, questions]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-100";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Actions */}
      <div className="flex items-center justify-between mb-12">
        <Button
          onClick={onBackToCertificate}
          variant="outline"
          className="flex items-center gap-2 border-gray-200 text-charcoal hover:bg-gray-50 rounded-xl h-12 px-6"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Certificate
        </Button>

        <div className="flex items-center gap-4">
          <Button
            onClick={() => window.print()}
            variant="outline"
            className="flex items-center gap-2 border-gray-200 text-charcoal hover:bg-gray-50 rounded-xl h-12 px-6"
          >
            <Download className="h-5 w-5" />
            Export Report
          </Button>

          <Button
            onClick={onRetakeExam}
            className="bg-gradient-to-r from-primary to-primary-deep hover:from-primary-deep hover:to-primary text-white flex items-center gap-2 rounded-xl h-12 px-8 shadow-lg hover:shadow-xl transition-all"
          >
            <RefreshCw className="h-5 w-5" />
            Retake Exam
          </Button>
        </div>
      </div>

      {/* Overall Score Section */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-10">
        {/* Main Score Card */}
        <div className="lg:col-span-3">
          <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="flex items-center gap-6">
              <div className={`p-4 rounded-2xl ${analytics.overallScore >= 70 ? 'bg-accent/10' : 'bg-red-500/10'} shadow-sm`}>
                <Trophy className={`h-12 w-12 ${analytics.overallScore >= 70 ? 'text-accent' : 'text-red-500'}`} />
              </div>
              <div className="flex-1">
                <div className="flex items-baseline gap-4 mb-2">
                  <h2 className="text-4xl font-bold text-charcoal">
                    {analytics.overallScore}%
                  </h2>
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-semibold ${
                    analytics.overallScore >= 70
                      ? 'bg-accent text-white'
                      : 'bg-red-500 text-white'
                  }`}>
                    {analytics.overallScore >= 70 ? "✓ Passed" : "✗ Failed"}
                  </div>
                </div>
                <p className="text-grey mb-3">{analytics.correctAnswers} of {analytics.totalQuestions} questions correct</p>

                {/* Compact Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      analytics.overallScore >= 70
                        ? 'bg-gradient-to-r from-accent to-primary'
                        : 'bg-gradient-to-r from-red-400 to-red-500'
                    }`}
                    style={{ width: `${analytics.overallScore}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Quick Stats */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-accent/10 rounded-lg p-2">
                  <CheckCircle className="h-4 w-4 text-accent" />
                </div>
                <span className="text-sm text-grey">Correct</span>
              </div>
              <span className="text-xl font-bold text-accent">{analytics.correctAnswers}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-red-100 rounded-lg p-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                </div>
                <span className="text-sm text-grey">Incorrect</span>
              </div>
              <span className="text-xl font-bold text-red-500">{analytics.incorrectAnswers}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 rounded-lg p-2">
                  <Clock className="h-4 w-4 text-primary" />
                </div>
                <span className="text-sm text-grey">Avg. Time</span>
              </div>
              <span className="text-xl font-bold text-primary">{formatTime(analytics.averageTimePerQuestion)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance by Difficulty - Modern Cards */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
          <div className="bg-primary/10 rounded-xl p-2">
            <BarChart3 className="h-6 w-6 text-primary" />
          </div>
          Performance by Difficulty
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {Object.entries(analytics.difficultyPerformance).map(([difficulty, performance]) => {
            const accuracy = performance.total > 0 ? (performance.correct / performance.total) * 100 : 0;
            const difficultyColors = {
              Easy: { bg: "from-accent/10 to-accent/20", border: "border-accent/30", text: "text-accent", icon: "bg-accent/10" },
              Medium: { bg: "from-yellow-100 to-yellow-200", border: "border-yellow-300", text: "text-yellow-700", icon: "bg-yellow-100" },
              Hard: { bg: "from-red-100 to-red-200", border: "border-red-300", text: "text-red-600", icon: "bg-red-100" }
            };
            const colors = difficultyColors[difficulty as keyof typeof difficultyColors];

            return (
              <div key={difficulty} className={`bg-gradient-to-br ${colors.bg} rounded-3xl p-8 border ${colors.border} shadow-lg`}>
                <div className="text-center">
                  <div className={`${colors.icon} rounded-2xl p-4 inline-flex mb-6`}>
                    <Target className={`h-8 w-8 ${colors.text}`} />
                  </div>
                  <h4 className="text-xl font-bold text-charcoal mb-2">{difficulty}</h4>
                  <p className={`text-5xl font-bold ${colors.text} mb-2`}>{Math.round(accuracy)}%</p>
                  <p className="text-grey font-medium mb-4">{performance.correct} of {performance.total} correct</p>

                  {/* Progress Circle */}
                  <div className="relative w-24 h-24 mx-auto mb-4">
                    <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        className="text-gray-200"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        strokeDasharray={`${accuracy * 2.51} 251`}
                        className={colors.text}
                        strokeLinecap="round"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className={`text-lg font-bold ${colors.text}`}>{Math.round(accuracy)}%</span>
                    </div>
                  </div>

                  <p className="text-sm text-grey">Avg: {formatTime(performance.avgTime)}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Category Performance - Modern Graph */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
          <div className="bg-primary/10 rounded-xl p-2">
            <Target className="h-6 w-6 text-primary" />
          </div>
          Performance by Category
        </h3>

        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <div className="grid gap-6">
            {Object.entries(analytics.categoryPerformance).map(([category, performance], index) => {
              const accuracy = (performance.correct / performance.total) * 100;
              const isStrongest = category === analytics.strongestCategory;
              const isWeakest = category === analytics.weakestCategory;

              return (
                <div key={category} className="relative">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full ${
                        isStrongest ? 'bg-accent' :
                        isWeakest ? 'bg-red-500' :
                        'bg-primary'
                      }`}></div>
                      <span className="font-semibold text-charcoal text-lg">{category}</span>
                      {isStrongest && (
                        <span className="bg-accent text-white text-xs px-2 py-1 rounded-full font-medium">
                          Strongest
                        </span>
                      )}
                      {isWeakest && (
                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Needs Focus
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-charcoal">{Math.round(accuracy)}%</span>
                      <p className="text-sm text-grey">{performance.correct} of {performance.total}</p>
                    </div>
                  </div>

                  <div className="relative">
                    <div className="w-full bg-gray-200 rounded-full h-4 shadow-inner">
                      <div
                        className={`h-4 rounded-full transition-all duration-1000 ${
                          isStrongest ? 'bg-gradient-to-r from-accent to-primary' :
                          isWeakest ? 'bg-gradient-to-r from-red-400 to-red-500' :
                          accuracy >= 80 ? 'bg-gradient-to-r from-accent to-primary' :
                          accuracy >= 60 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                          'bg-gradient-to-r from-red-400 to-red-500'
                        }`}
                        style={{
                          width: `${accuracy}%`,
                          animationDelay: `${index * 200}ms`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Category Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 pt-8 border-t border-gray-200">
            <div className="bg-gradient-to-br from-accent/10 to-accent/20 rounded-2xl p-6 border border-accent/30">
              <div className="flex items-center gap-3 mb-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span className="font-semibold text-charcoal">Strongest Category</span>
              </div>
              <p className="text-xl font-bold text-accent">{analytics.strongestCategory}</p>
              <p className="text-sm text-grey">
                {Math.round((analytics.categoryPerformance[analytics.strongestCategory]?.correct / analytics.categoryPerformance[analytics.strongestCategory]?.total) * 100)}% accuracy
              </p>
            </div>

            <div className="bg-gradient-to-br from-red-100 to-red-200 rounded-2xl p-6 border border-red-300">
              <div className="flex items-center gap-3 mb-2">
                <Flag className="h-5 w-5 text-red-600" />
                <span className="font-semibold text-charcoal">Focus Area</span>
              </div>
              <p className="text-xl font-bold text-red-600">{analytics.weakestCategory}</p>
              <p className="text-sm text-grey">
                {Math.round((analytics.categoryPerformance[analytics.weakestCategory]?.correct / analytics.categoryPerformance[analytics.weakestCategory]?.total) * 100)}% accuracy
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Overview Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Question Performance Summary */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
            <div className="bg-primary/10 rounded-xl p-2">
              <Target className="h-6 w-6 text-primary" />
            </div>
            Question Performance
          </h3>

          <div className="space-y-4">
            {/* Performance Grid */}
            <div className="grid grid-cols-5 gap-2">
              {questions.slice(0, 25).map((question, index) => {
                const answer = attempt.answers.find(a => a.questionId === question.id);
                const isAnswered = !!answer;
                const isCorrect = answer?.isCorrect || false;

                return (
                  <div
                    key={question.id}
                    className={`aspect-square rounded-lg flex items-center justify-center text-sm font-bold cursor-pointer transition-all hover:scale-110 hover:shadow-lg ${
                      !isAnswered
                        ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        : isCorrect
                        ? 'bg-accent text-white hover:bg-accent/80'
                        : 'bg-red-500 text-white hover:bg-red-400'
                    }`}
                    onClick={() => {
                      const questionElement = document.getElementById(`question-${index}`);
                      if (questionElement) {
                        questionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        questionElement.classList.add('ring-4', 'ring-primary/30', 'ring-offset-2');
                        setTimeout(() => {
                          questionElement.classList.remove('ring-4', 'ring-primary/30', 'ring-offset-2');
                        }, 2000);
                      }
                    }}
                    title={`Question ${index + 1}: ${!isAnswered ? 'Not Answered' : isCorrect ? 'Correct' : 'Incorrect'}`}
                  >
                    {index + 1}
                  </div>
                );
              })}
            </div>

            {/* Legend */}
            <div className="flex items-center justify-center gap-6 text-sm pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-accent rounded"></div>
                <span className="text-charcoal font-medium">Correct</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-500 rounded"></div>
                <span className="text-charcoal font-medium">Incorrect</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gray-300 rounded"></div>
                <span className="text-charcoal font-medium">Unanswered</span>
              </div>
            </div>
          </div>
        </div>

        {/* Exam Momentum Analysis */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
            <div className="bg-primary/10 rounded-xl p-2">
              <TrendingUp className="h-6 w-6 text-primary" />
            </div>
            Exam Momentum & Focus
          </h3>

          <div className="space-y-8">
            {/* Performance Timeline */}
            <div className="bg-gray-50 rounded-2xl p-6">
              <h4 className="font-bold text-charcoal mb-4">Performance Throughout Exam</h4>
              <div className="relative">
                {/* Timeline Line */}
                <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-300 transform -translate-y-1/2"></div>

                {/* Performance Points */}
                <div className="flex justify-between items-center relative z-10">
                  {analytics.examMomentum.slice(0, 20).map((point, index) => {
                    const question = questions[point.questionIndex];
                    const isSlowQuestion = point.timeSpent > analytics.averageTimePerQuestion * 1.5;
                    const isFastQuestion = point.timeSpent < analytics.averageTimePerQuestion * 0.7;

                    return (
                      <div
                        key={index}
                        className="relative group cursor-pointer"
                        onClick={() => {
                          const questionElement = document.getElementById(`question-${point.questionIndex}`);
                          if (questionElement) {
                            questionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                          }
                        }}
                      >
                        {/* Performance Point */}
                        <div className={`w-4 h-4 rounded-full border-2 border-white shadow-lg transition-all hover:scale-150 ${
                          point.isCorrect
                            ? isSlowQuestion
                              ? 'bg-yellow-500' // Correct but slow
                              : 'bg-accent' // Correct and good time
                            : isFastQuestion
                              ? 'bg-orange-500' // Incorrect and rushed
                              : 'bg-red-500' // Incorrect
                        }`}></div>

                        {/* Hover Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-20">
                          <div className="bg-charcoal text-white text-xs rounded-lg p-3 shadow-xl min-w-48 whitespace-nowrap">
                            <p className="font-semibold">Q{point.questionIndex + 1}: {point.isCorrect ? '✓ Correct' : '✗ Incorrect'}</p>
                            <p className="text-gray-300">Time: {formatTime(point.timeSpent)}</p>
                            <p className="text-gray-300">
                              {isSlowQuestion ? '🐌 Took extra time' : isFastQuestion ? '⚡ Answered quickly' : '⏱️ Normal pace'}
                            </p>
                            {question?.topic && <p className="text-gray-300">Topic: {question.topic}</p>}
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-charcoal"></div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Timeline Labels */}
                <div className="flex justify-between mt-4 text-xs text-grey">
                  <span>Start</span>
                  <span>Middle</span>
                  <span>End</span>
                </div>
              </div>

              {/* Legend */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-accent rounded-full"></div>
                  <span>Correct & Good Pace</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span>Correct but Slow</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span>Incorrect & Rushed</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Incorrect</span>
                </div>
              </div>
            </div>

            {/* Performance Streaks */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <h4 className="font-bold text-charcoal mb-4 flex items-center gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full"></div>
                  Best Streak
                </h4>
                {(() => {
                  let maxStreak = 0;
                  let currentStreak = 0;
                  let bestStreakStart = 0;
                  let currentStart = 0;

                  analytics.examMomentum.forEach((point, index) => {
                    if (point.isCorrect) {
                      if (currentStreak === 0) currentStart = index;
                      currentStreak++;
                      if (currentStreak > maxStreak) {
                        maxStreak = currentStreak;
                        bestStreakStart = currentStart;
                      }
                    } else {
                      currentStreak = 0;
                    }
                  });

                  return (
                    <div>
                      <p className="text-2xl font-bold text-accent mb-1">{maxStreak} questions</p>
                      <p className="text-sm text-grey">
                        Questions {bestStreakStart + 1}-{bestStreakStart + maxStreak}
                      </p>
                    </div>
                  );
                })()}
              </div>

              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <h4 className="font-bold text-charcoal mb-4 flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  Focus Pattern
                </h4>
                {(() => {
                  const totalQuestions = analytics.examMomentum.length;
                  const firstThird = analytics.examMomentum.slice(0, Math.floor(totalQuestions / 3));
                  const middleThird = analytics.examMomentum.slice(Math.floor(totalQuestions / 3), Math.floor(totalQuestions * 2 / 3));
                  const lastThird = analytics.examMomentum.slice(Math.floor(totalQuestions * 2 / 3));

                  const firstThirdAccuracy = firstThird.length > 0 ? (firstThird.filter(p => p.isCorrect).length / firstThird.length) * 100 : 0;
                  const middleThirdAccuracy = middleThird.length > 0 ? (middleThird.filter(p => p.isCorrect).length / middleThird.length) * 100 : 0;
                  const lastThirdAccuracy = lastThird.length > 0 ? (lastThird.filter(p => p.isCorrect).length / lastThird.length) * 100 : 0;

                  const bestPeriod = Math.max(firstThirdAccuracy, middleThirdAccuracy, lastThirdAccuracy);
                  const bestPeriodName = bestPeriod === firstThirdAccuracy ? 'Strong Start' :
                                        bestPeriod === middleThirdAccuracy ? 'Steady Middle' : 'Strong Finish';

                  return (
                    <div>
                      <p className="text-2xl font-bold text-primary mb-1">{bestPeriodName}</p>
                      <p className="text-sm text-grey">{Math.round(bestPeriod)}% accuracy in best period</p>
                    </div>
                  );
                })()}
              </div>
            </div>

            {/* Time Analysis Summary */}
            <div className="bg-white rounded-xl p-6 border border-gray-200">
              <h4 className="font-bold text-charcoal mb-4">Time Management</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-primary">{formatTime(attempt.timeSpentSeconds || 0)}</p>
                  <p className="text-sm text-grey">Total Time</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-accent">{formatTime(analytics.averageTimePerQuestion)}</p>
                  <p className="text-sm text-grey">Avg per Question</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-600">
                    {analytics.examMomentum.length > 0
                      ? formatTime(Math.max(...analytics.examMomentum.map(m => m.timeSpent)))
                      : formatTime(0)
                    }
                  </p>
                  <p className="text-sm text-grey">Longest Question</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Previous Attempts Comparison */}
      {previousAttempts.length > 0 && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
            <div className="bg-primary/10 rounded-xl p-2">
              <TrendingUp className="h-6 w-6 text-primary" />
            </div>
            Previous Attempts Comparison
          </h3>

          <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
            <div className="grid gap-6">
              {/* Current vs Previous Performance */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl p-6 border border-primary/30">
                  <h4 className="text-lg font-bold text-charcoal mb-4">Current Attempt</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-grey">Score</span>
                      <span className="text-2xl font-bold text-primary">{analytics.overallScore}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-grey">Time</span>
                      <span className="font-semibold text-charcoal">{formatTime(attempt.timeSpentSeconds || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-grey">Correct</span>
                      <span className="font-semibold text-charcoal">{analytics.correctAnswers}/{analytics.totalQuestions}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                  <h4 className="text-lg font-bold text-charcoal mb-4">Best Previous</h4>
                  {(() => {
                    const bestAttempt = previousAttempts.reduce((best, current) =>
                      (current.score || 0) > (best.score || 0) ? current : best
                    );
                    return (
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-grey">Score</span>
                          <span className="text-2xl font-bold text-accent">{bestAttempt.score || 0}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-grey">Time</span>
                          <span className="font-semibold text-charcoal">{formatTime(bestAttempt.timeSpentSeconds || 0)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-grey">Correct</span>
                          <span className="font-semibold text-charcoal">{bestAttempt.correctAnswers || 0}/{bestAttempt.totalQuestions || 0}</span>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>

              {/* Progress Chart */}
              <div>
                <h4 className="text-lg font-bold text-charcoal mb-4">Score Progress Over Time</h4>
                <div className="bg-gray-50 rounded-2xl p-6">
                  <div className="flex items-end justify-between h-32">
                    {[...previousAttempts.slice(-5), { score: analytics.overallScore, id: 'current' }].map((attemptData, index, array) => {
                      const isCurrentAttempt = attemptData.id === 'current';
                      const score = attemptData.score || 0;
                      const height = Math.max(20, (score / 100) * 80);

                      return (
                        <div key={attemptData.id} className="flex flex-col items-center">
                          <div
                            className={`w-8 rounded-t-lg transition-all duration-500 ${
                              isCurrentAttempt
                                ? 'bg-gradient-to-t from-primary to-accent shadow-lg'
                                : score >= 70
                                ? 'bg-gradient-to-t from-accent to-primary'
                                : 'bg-gradient-to-t from-red-400 to-red-500'
                            }`}
                            style={{
                              height: `${height}%`,
                              animationDelay: `${index * 200}ms`
                            }}
                            title={`${isCurrentAttempt ? 'Current' : `Attempt ${index + 1}`}: ${score}%`}
                          ></div>
                          <span className="text-xs text-grey mt-2">
                            {isCurrentAttempt ? 'Current' : `#${index + 1}`}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Improvement Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {(() => {
                  const lastAttempt = previousAttempts[previousAttempts.length - 1];
                  const scoreImprovement = lastAttempt ? analytics.overallScore - (lastAttempt.score || 0) : 0;
                  const timeImprovement = lastAttempt ? (lastAttempt.timeSpentSeconds || 0) - (attempt.timeSpentSeconds || 0) : 0;
                  const accuracyImprovement = lastAttempt ? analytics.correctAnswers - (lastAttempt.correctAnswers || 0) : 0;

                  return (
                    <>
                      <div className={`p-4 rounded-xl border ${scoreImprovement >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center gap-2 mb-1">
                          <TrendingUp className={`h-4 w-4 ${scoreImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                          <span className="text-sm font-medium text-charcoal">Score Change</span>
                        </div>
                        <p className={`text-lg font-bold ${scoreImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {scoreImprovement >= 0 ? '+' : ''}{scoreImprovement.toFixed(1)}%
                        </p>
                      </div>

                      <div className={`p-4 rounded-xl border ${timeImprovement >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center gap-2 mb-1">
                          <Clock className={`h-4 w-4 ${timeImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                          <span className="text-sm font-medium text-charcoal">Time Change</span>
                        </div>
                        <p className={`text-lg font-bold ${timeImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {timeImprovement >= 0 ? '+' : ''}{formatTime(Math.abs(timeImprovement))}
                        </p>
                      </div>

                      <div className={`p-4 rounded-xl border ${accuracyImprovement >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center gap-2 mb-1">
                          <Target className={`h-4 w-4 ${accuracyImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                          <span className="text-sm font-medium text-charcoal">Accuracy Change</span>
                        </div>
                        <p className={`text-lg font-bold ${accuracyImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {accuracyImprovement >= 0 ? '+' : ''}{accuracyImprovement} questions
                        </p>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Smart Question Review */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
          <div className="bg-primary/10 rounded-xl p-2">
            <BookOpen className="h-6 w-6 text-primary" />
          </div>
          Question Review
        </h3>

        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          {/* Filter Tabs */}
          <div className="flex flex-wrap gap-2 mb-8">
            {[
              { key: 'all', label: 'All Questions', count: questions.length },
              { key: 'incorrect', label: 'Incorrect', count: analytics.incorrectAnswers },
              { key: 'correct', label: 'Correct', count: analytics.correctAnswers },
              { key: 'unanswered', label: 'Unanswered', count: questions.length - attempt.answers.length }
            ].map((filter) => (
              <button
                key={filter.key}
                onClick={() => setSelectedFilter(filter.key as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  selectedFilter === filter.key
                    ? 'bg-primary text-white shadow-md'
                    : 'bg-gray-100 text-charcoal hover:bg-gray-200'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>

          {/* Question Cards Grid */}
          <div className="grid gap-4">
            {filteredQuestions.map((question, index) => {
              const answer = attempt.answers.find(a => a.questionId === question.id);
              const isAnswered = !!answer;
              const isCorrect = answer?.isCorrect || false;
              const timeSpent = answer?.timeSpent || 0;
              const originalIndex = questions.findIndex(q => q.id === question.id);

              return (
                <div
                  key={question.id}
                  id={`question-${originalIndex}`}
                  className={`border rounded-2xl p-6 transition-all hover:shadow-md scroll-mt-24 ${
                    !isAnswered
                      ? 'border-gray-300 bg-gray-50'
                      : isCorrect
                      ? 'border-accent/30 bg-accent/5'
                      : 'border-red-300 bg-red-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    {/* Left: Question Info */}
                    <div className="flex items-center gap-4">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center font-bold ${
                        !isAnswered
                          ? 'bg-gray-200 text-gray-600'
                          : isCorrect
                          ? 'bg-accent text-white'
                          : 'bg-red-500 text-white'
                      }`}>
                        {originalIndex + 1}
                      </div>

                      <div>
                        <h4 className="font-semibold text-charcoal">Question {originalIndex + 1}</h4>
                        <div className="flex items-center gap-2 text-sm text-grey">
                          {question.topic && (
                            <span className="bg-gray-100 px-2 py-1 rounded text-xs">{question.topic}</span>
                          )}
                          {question.difficulty && (
                            <span className={`px-2 py-1 rounded text-xs ${
                              question.difficulty === 'Easy' ? 'bg-accent/20 text-accent' :
                              question.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-red-100 text-red-600'
                            }`}>
                              {question.difficulty}
                            </span>
                          )}
                          {timeSpent > 0 && (
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTime(timeSpent)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Right: Answer Summary */}
                    <div className="text-right">
                      <div className={`px-3 py-1 rounded-full text-sm font-medium mb-2 ${
                        !isAnswered
                          ? 'bg-gray-200 text-gray-600'
                          : isCorrect
                          ? 'bg-accent text-white'
                          : 'bg-red-500 text-white'
                      }`}>
                        {!isAnswered ? 'Not Answered' : isCorrect ? 'Correct' : 'Incorrect'}
                      </div>
                      {answer && (
                        <div className="text-sm text-grey">
                          Your answer: <span className="font-medium text-charcoal">{answer.selectedAnswer}</span>
                          {!isCorrect && (
                            <span> • Correct: <span className="font-medium text-accent">{question.correct}</span></span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Expandable Question Details */}
                  <details className="mt-4">
                    <summary className="cursor-pointer text-primary font-medium hover:text-primary-deep">
                      View Question Details
                    </summary>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-charcoal mb-4 leading-relaxed">{question.question}</p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {(['A', 'B', 'C', 'D'] as const).map((option) => {
                          const isSelected = answer?.selectedAnswer === option;
                          const isCorrectAnswer = question.correct === option;
                          const optionIndexMap: Record<'A' | 'B' | 'C' | 'D', number> = { A: 0, B: 1, C: 2, D: 3 };
                          const optionText = question.choices?.[optionIndexMap[option]] ?? '';

                          return (
                            <div
                              key={option}
                              className={`p-3 rounded-lg border text-sm ${
                                isCorrectAnswer
                                  ? 'border-accent bg-accent/10 text-accent'
                                  : isSelected && !isCorrect
                                  ? 'border-red-300 bg-red-50 text-red-700'
                                  : 'border-gray-200 bg-white text-charcoal'
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                <span className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${
                                  isCorrectAnswer
                                    ? 'bg-accent text-white'
                                    : isSelected && !isCorrect
                                    ? 'bg-red-500 text-white'
                                    : 'bg-gray-200 text-gray-600'
                                }`}>
                                  {option}
                                </span>
                                <span className="flex-1">{optionText}</span>
                                {isCorrectAnswer && <CheckCircle className="h-4 w-4 text-accent" />}
                                {isSelected && !isCorrect && <XCircle className="h-4 w-4 text-red-500" />}
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {question.explanation && (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <h5 className="font-semibold text-blue-800 mb-2">Explanation:</h5>
                          <p className="text-blue-700 text-sm leading-relaxed">{question.explanation}</p>
                        </div>
                      )}
                    </div>
                  </details>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
