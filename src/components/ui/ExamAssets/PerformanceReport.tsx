"use client";

import { useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { type ExamAttempt } from "@/Services/examAttemptsService";
import { type QuestionRecord } from "@/Services/questionsService";
import { SimpleChart } from "./SimpleChart";
import {
  Trophy,
  Target,
  Clock,
  TrendingUp,
  BarChart3,
  Download,
  RefreshCw,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Flag,
  PieChart
} from "lucide-react";

interface PerformanceReportProps {
  attempt: ExamAttempt;
  questions: QuestionRecord[];
  onRetakeExam: () => void;
  onBackToCertificate: () => void;
}

interface PerformanceAnalytics {
  overallScore: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalQuestions: number;
  averageTimePerQuestion: number;
  difficultyPerformance: {
    Easy: { correct: number; total: number; avgTime: number };
    Medium: { correct: number; total: number; avgTime: number };
    Hard: { correct: number; total: number; avgTime: number };
  };
  categoryPerformance: Record<string, { correct: number; total: number }>;
  strongestCategory: string;
  weakestCategory: string;
  examMomentum: { questionIndex: number; isCorrect: boolean; timeSpent: number }[];
}

export function PerformanceReport({ 
  attempt, 
  questions, 
  onRetakeExam, 
  onBackToCertificate 
}: PerformanceReportProps) {
  
  const analytics: PerformanceAnalytics = useMemo(() => {
    const correctAnswers = attempt.correctAnswers || 0;
    const totalQuestions = attempt.totalQuestions;
    const overallScore = attempt.score || 0;
    
    // Calculate average time per question
    const totalTimeSpent = attempt.timeSpentSeconds || 0;
    const averageTimePerQuestion = totalTimeSpent / totalQuestions;

    // Initialize difficulty performance
    const difficultyPerformance = {
      Easy: { correct: 0, total: 0, avgTime: 0 },
      Medium: { correct: 0, total: 0, avgTime: 0 },
      Hard: { correct: 0, total: 0, avgTime: 0 },
    };

    // Initialize category performance
    const categoryPerformance: Record<string, { correct: number; total: number }> = {};

    // Process each question and answer
    const examMomentum: { questionIndex: number; isCorrect: boolean; timeSpent: number }[] = [];
    
    questions.forEach((question, index) => {
      const answer = attempt.answers.find(a => a.questionId === question.id);
      const isCorrect = answer?.isCorrect || false;
      const timeSpent = answer?.timeSpent || averageTimePerQuestion;

      // Difficulty analysis
      const difficulty = question.difficulty as keyof typeof difficultyPerformance;
      if (difficulty && difficultyPerformance[difficulty]) {
        difficultyPerformance[difficulty].total++;
        if (isCorrect) difficultyPerformance[difficulty].correct++;
        difficultyPerformance[difficulty].avgTime += timeSpent;
      }

      // Category analysis
      const category = question.topic || "General";
      if (!categoryPerformance[category]) {
        categoryPerformance[category] = { correct: 0, total: 0 };
      }
      categoryPerformance[category].total++;
      if (isCorrect) categoryPerformance[category].correct++;

      // Momentum tracking
      examMomentum.push({
        questionIndex: index,
        isCorrect,
        timeSpent,
      });
    });

    // Calculate average times for difficulties
    Object.keys(difficultyPerformance).forEach(key => {
      const diff = difficultyPerformance[key as keyof typeof difficultyPerformance];
      if (diff.total > 0) {
        diff.avgTime = diff.avgTime / diff.total;
      }
    });

    // Find strongest and weakest categories
    let strongestCategory = "";
    let weakestCategory = "";
    let highestAccuracy = 0;
    let lowestAccuracy = 1;

    Object.entries(categoryPerformance).forEach(([category, performance]) => {
      const accuracy = performance.correct / performance.total;
      if (accuracy > highestAccuracy) {
        highestAccuracy = accuracy;
        strongestCategory = category;
      }
      if (accuracy < lowestAccuracy) {
        lowestAccuracy = accuracy;
        weakestCategory = category;
      }
    });

    return {
      overallScore,
      correctAnswers,
      incorrectAnswers: totalQuestions - correctAnswers,
      totalQuestions,
      averageTimePerQuestion,
      difficultyPerformance,
      categoryPerformance,
      strongestCategory,
      weakestCategory,
      examMomentum,
    };
  }, [attempt, questions]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-100";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Actions */}
      <div className="flex items-center justify-between mb-8">
        <Button
          onClick={onBackToCertificate}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Certificate
        </Button>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={() => window.print()}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Report
          </Button>
          
          <Button
            onClick={onRetakeExam}
            className="bg-primary hover:bg-primary-deep text-white flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retake Exam
          </Button>
        </div>
      </div>

      {/* Overall Score Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* Main Score Card */}
        <div className="lg:col-span-2">
          <div className={`${getScoreBgColor(analytics.overallScore)} rounded-2xl p-8 border-2 ${analytics.overallScore >= 70 ? 'border-green-200' : 'border-red-200'}`}>
            <div className="flex items-center gap-6">
              <div className={`p-4 rounded-2xl ${analytics.overallScore >= 70 ? 'bg-green-200' : 'bg-red-200'}`}>
                <Trophy className={`h-12 w-12 ${getScoreColor(analytics.overallScore)}`} />
              </div>
              <div>
                <h2 className="text-4xl font-bold text-charcoal mb-2">
                  {analytics.overallScore}%
                </h2>
                <p className="text-lg text-grey mb-1">Overall Score</p>
                <p className={`text-sm font-medium ${getScoreColor(analytics.overallScore)}`}>
                  {analytics.overallScore >= 70 ? "Passed" : "Failed"} • {analytics.correctAnswers} of {analytics.totalQuestions} correct
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="mt-6">
              <div className="w-full bg-white rounded-full h-4 shadow-inner">
                <div 
                  className={`h-4 rounded-full transition-all duration-1000 ${
                    analytics.overallScore >= 70 ? 'bg-gradient-to-r from-green-400 to-green-500' : 'bg-gradient-to-r from-red-400 to-red-500'
                  }`}
                  style={{ width: `${analytics.overallScore}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 rounded-lg p-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">{analytics.correctAnswers}</p>
                <p className="text-sm text-grey">Correct Answers</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-center gap-3">
              <div className="bg-red-100 rounded-lg p-2">
                <XCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-red-600">{analytics.incorrectAnswers}</p>
                <p className="text-sm text-grey">Incorrect Answers</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 rounded-lg p-2">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600">{formatTime(analytics.averageTimePerQuestion)}</p>
                <p className="text-sm text-grey">Avg. Time/Question</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance by Difficulty */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8 mb-8">
        <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Performance by Difficulty
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Object.entries(analytics.difficultyPerformance).map(([difficulty, performance]) => {
            const accuracy = performance.total > 0 ? (performance.correct / performance.total) * 100 : 0;
            return (
              <div key={difficulty} className="text-center">
                <div className={`p-4 rounded-lg mb-4 ${
                  difficulty === "Easy" ? "bg-green-50" : 
                  difficulty === "Medium" ? "bg-yellow-50" : "bg-red-50"
                }`}>
                  <h4 className="font-semibold text-charcoal mb-2">{difficulty}</h4>
                  <p className="text-3xl font-bold text-charcoal mb-1">{Math.round(accuracy)}%</p>
                  <p className="text-sm text-grey">{performance.correct} of {performance.total}</p>
                  <p className="text-xs text-grey mt-1">Avg: {formatTime(performance.avgTime)}</p>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      difficulty === "Easy" ? "bg-green-400" : 
                      difficulty === "Medium" ? "bg-yellow-400" : "bg-red-400"
                    }`}
                    style={{ width: `${accuracy}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Visual Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Score Breakdown Pie Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Score Breakdown
          </h3>

          <SimpleChart
            type="pie"
            height={250}
            data={[
              { label: "Correct", value: analytics.correctAnswers, color: "fill-green-400" },
              { label: "Incorrect", value: analytics.incorrectAnswers, color: "fill-red-400" },
            ]}
          />
        </div>

        {/* Difficulty Performance Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Difficulty Performance
          </h3>

          <SimpleChart
            type="bar"
            height={250}
            data={Object.entries(analytics.difficultyPerformance).map(([difficulty, performance]) => ({
              label: difficulty,
              value: performance.total > 0 ? (performance.correct / performance.total) * 100 : 0,
              color: difficulty === "Easy" ? "bg-green-400" :
                     difficulty === "Medium" ? "bg-yellow-400" : "bg-red-400"
            }))}
          />
        </div>
      </div>

      {/* Category Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
            <Target className="h-5 w-5" />
            Category Performance
          </h3>

          <div className="space-y-4">
            {Object.entries(analytics.categoryPerformance).map(([category, performance]) => {
              const accuracy = (performance.correct / performance.total) * 100;
              return (
                <div key={category} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-charcoal">{category}</span>
                      <span className="text-sm text-grey">{Math.round(accuracy)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          accuracy >= 80 ? "bg-green-400" :
                          accuracy >= 60 ? "bg-yellow-400" : "bg-red-400"
                        }`}
                        style={{ width: `${accuracy}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-grey mt-1">{performance.correct} of {performance.total} correct</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Insights and Recommendations */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Insights & Recommendations
          </h3>

          <div className="space-y-6">
            {/* Strongest Category */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="font-semibold text-green-800">Strongest Area</span>
              </div>
              <p className="text-green-700">{analytics.strongestCategory}</p>
              <p className="text-sm text-green-600 mt-1">
                {Math.round((analytics.categoryPerformance[analytics.strongestCategory]?.correct / analytics.categoryPerformance[analytics.strongestCategory]?.total) * 100)}% accuracy
              </p>
            </div>

            {/* Weakest Category */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Flag className="h-4 w-4 text-red-600" />
                <span className="font-semibold text-red-800">Needs Improvement</span>
              </div>
              <p className="text-red-700">{analytics.weakestCategory}</p>
              <p className="text-sm text-red-600 mt-1">
                {Math.round((analytics.categoryPerformance[analytics.weakestCategory]?.correct / analytics.categoryPerformance[analytics.weakestCategory]?.total) * 100)}% accuracy
              </p>
            </div>

            {/* Recommendations */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">Recommendations</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Focus on {analytics.weakestCategory} topics for improvement</li>
                <li>• Review incorrect answers to identify knowledge gaps</li>
                <li>• Practice more {Object.entries(analytics.difficultyPerformance)
                  .filter(([_, perf]) => perf.total > 0 && (perf.correct / perf.total) < 0.7)
                  .map(([diff]) => diff.toLowerCase())
                  .join(" and ")} questions</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Exam Momentum and Time Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Exam Momentum Graph */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Exam Momentum
          </h3>

          <div className="relative">
            <div className="flex items-end justify-between h-32 mb-4">
              {analytics.examMomentum.slice(0, 15).map((point, index) => (
                <div
                  key={index}
                  className={`w-4 rounded-t transition-all duration-300 ${
                    point.isCorrect ? "bg-green-400" : "bg-red-400"
                  }`}
                  style={{
                    height: `${Math.max(20, (point.timeSpent / analytics.averageTimePerQuestion) * 50)}%`
                  }}
                  title={`Question ${point.questionIndex + 1}: ${point.isCorrect ? "Correct" : "Incorrect"} (${formatTime(point.timeSpent)})`}
                ></div>
              ))}
            </div>
            <div className="flex justify-between text-xs text-grey">
              <span>Question 1</span>
              <span>Question {Math.min(15, analytics.totalQuestions)}</span>
            </div>
            <div className="mt-4 flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-400 rounded"></div>
                <span className="text-grey">Correct</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-400 rounded"></div>
                <span className="text-grey">Incorrect</span>
              </div>
              <span className="text-grey">Height = Time Spent</span>
            </div>
          </div>
        </div>

        {/* Time Analysis */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-charcoal mb-6 flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Analysis
          </h3>

          <div className="space-y-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-primary">{formatTime(attempt.timeSpentSeconds || 0)}</p>
              <p className="text-sm text-grey">Total Time Spent</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <p className="text-lg font-semibold text-blue-600">{formatTime(analytics.averageTimePerQuestion)}</p>
                <p className="text-xs text-blue-600">Avg per Question</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <p className="text-lg font-semibold text-purple-600">
                  {formatTime(Math.max(...analytics.examMomentum.map(m => m.timeSpent)))}
                </p>
                <p className="text-xs text-purple-600">Longest Question</p>
              </div>
            </div>

            {/* Time by Difficulty */}
            <div className="space-y-2">
              <h4 className="font-medium text-charcoal">Average Time by Difficulty</h4>
              {Object.entries(analytics.difficultyPerformance).map(([difficulty, performance]) => (
                <div key={difficulty} className="flex items-center justify-between">
                  <span className="text-sm text-grey">{difficulty}</span>
                  <span className="text-sm font-medium text-charcoal">
                    {formatTime(performance.avgTime)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
