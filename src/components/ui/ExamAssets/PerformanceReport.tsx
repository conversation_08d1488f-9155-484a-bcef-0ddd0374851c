"use client";

import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { type ExamAttempt } from "@/Services/examAttemptsService";
import { type QuestionRecord } from "@/Services/questionsService";
import { SimpleChart } from "./SimpleChart";
import {
  Trophy,
  Target,
  Clock,
  TrendingUp,
  BarChart3,
  Download,
  RefreshCw,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Flag,
  PieChart,
  BookOpen
} from "lucide-react";

interface PerformanceReportProps {
  attempt: ExamAttempt;
  questions: QuestionRecord[];
  previousAttempts?: any[]; // Previous attempt summaries
  onRetakeExam: () => void;
  onBackToCertificate: () => void;
}

interface PerformanceAnalytics {
  overallScore: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalQuestions: number;
  averageTimePerQuestion: number;
  difficultyPerformance: {
    Easy: { correct: number; total: number; avgTime: number };
    Medium: { correct: number; total: number; avgTime: number };
    Hard: { correct: number; total: number; avgTime: number };
  };
  categoryPerformance: Record<string, { correct: number; total: number }>;
  strongestCategory: string;
  weakestCategory: string;
  examMomentum: { questionIndex: number; isCorrect: boolean; timeSpent: number }[];
}

export function PerformanceReport({
  attempt,
  questions,
  previousAttempts = [],
  onRetakeExam,
  onBackToCertificate
}: PerformanceReportProps) {
  const [hoveredQuestion, setHoveredQuestion] = useState<number | null>(null);
  
  const analytics: PerformanceAnalytics = useMemo(() => {
    const correctAnswers = attempt.correctAnswers || 0;
    const totalQuestions = attempt.totalQuestions;
    const overallScore = attempt.score || 0;
    
    // Calculate average time per question
    const totalTimeSpent = attempt.timeSpentSeconds || 0;
    const averageTimePerQuestion = totalTimeSpent / totalQuestions;

    // Initialize difficulty performance
    const difficultyPerformance = {
      Easy: { correct: 0, total: 0, avgTime: 0 },
      Medium: { correct: 0, total: 0, avgTime: 0 },
      Hard: { correct: 0, total: 0, avgTime: 0 },
    };

    // Initialize category performance
    const categoryPerformance: Record<string, { correct: number; total: number }> = {};

    // Process each question and answer
    const examMomentum: { questionIndex: number; isCorrect: boolean; timeSpent: number }[] = [];
    
    questions.forEach((question, index) => {
      const answer = attempt.answers.find(a => a.questionId === question.id);
      const isCorrect = answer?.isCorrect || false;
      const timeSpent = answer?.timeSpent || 0;

      // Difficulty analysis
      const difficulty = question.difficulty as keyof typeof difficultyPerformance;
      if (difficulty && difficultyPerformance[difficulty]) {
        difficultyPerformance[difficulty].total++;
        if (isCorrect) difficultyPerformance[difficulty].correct++;
        if (timeSpent > 0) {
          difficultyPerformance[difficulty].avgTime += timeSpent;
        }
      }

      // Category analysis
      const category = question.topic || "General";
      if (!categoryPerformance[category]) {
        categoryPerformance[category] = { correct: 0, total: 0 };
      }
      categoryPerformance[category].total++;
      if (isCorrect) categoryPerformance[category].correct++;

      // Momentum tracking - only include answered questions with valid time
      if (answer && timeSpent > 0) {
        examMomentum.push({
          questionIndex: index,
          isCorrect,
          timeSpent,
        });
      }
    });

    // Calculate average times for difficulties
    Object.keys(difficultyPerformance).forEach(key => {
      const diff = difficultyPerformance[key as keyof typeof difficultyPerformance];
      if (diff.total > 0) {
        diff.avgTime = diff.avgTime / diff.total;
      }
    });

    // Find strongest and weakest categories
    let strongestCategory = "";
    let weakestCategory = "";
    let highestAccuracy = 0;
    let lowestAccuracy = 1;

    Object.entries(categoryPerformance).forEach(([category, performance]) => {
      const accuracy = performance.correct / performance.total;
      if (accuracy > highestAccuracy) {
        highestAccuracy = accuracy;
        strongestCategory = category;
      }
      if (accuracy < lowestAccuracy) {
        lowestAccuracy = accuracy;
        weakestCategory = category;
      }
    });

    return {
      overallScore,
      correctAnswers,
      incorrectAnswers: totalQuestions - correctAnswers,
      totalQuestions,
      averageTimePerQuestion,
      difficultyPerformance,
      categoryPerformance,
      strongestCategory,
      weakestCategory,
      examMomentum,
    };
  }, [attempt, questions]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-100";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Actions */}
      <div className="flex items-center justify-between mb-12">
        <Button
          onClick={onBackToCertificate}
          variant="outline"
          className="flex items-center gap-2 border-gray-200 text-charcoal hover:bg-gray-50 rounded-xl h-12 px-6"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Certificate
        </Button>

        <div className="flex items-center gap-4">
          <Button
            onClick={() => window.print()}
            variant="outline"
            className="flex items-center gap-2 border-gray-200 text-charcoal hover:bg-gray-50 rounded-xl h-12 px-6"
          >
            <Download className="h-5 w-5" />
            Export Report
          </Button>

          <Button
            onClick={onRetakeExam}
            className="bg-gradient-to-r from-primary to-primary-deep hover:from-primary-deep hover:to-primary text-white flex items-center gap-2 rounded-xl h-12 px-8 shadow-lg hover:shadow-xl transition-all"
          >
            <RefreshCw className="h-5 w-5" />
            Retake Exam
          </Button>
        </div>
      </div>

      {/* Overall Score Section */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-10">
        {/* Main Score Card */}
        <div className="lg:col-span-3">
          <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="flex items-center gap-6">
              <div className={`p-4 rounded-2xl ${analytics.overallScore >= 70 ? 'bg-accent/10' : 'bg-red-500/10'} shadow-sm`}>
                <Trophy className={`h-12 w-12 ${analytics.overallScore >= 70 ? 'text-accent' : 'text-red-500'}`} />
              </div>
              <div className="flex-1">
                <div className="flex items-baseline gap-4 mb-2">
                  <h2 className="text-4xl font-bold text-charcoal">
                    {analytics.overallScore}%
                  </h2>
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-semibold ${
                    analytics.overallScore >= 70
                      ? 'bg-accent text-white'
                      : 'bg-red-500 text-white'
                  }`}>
                    {analytics.overallScore >= 70 ? "✓ Passed" : "✗ Failed"}
                  </div>
                </div>
                <p className="text-grey mb-3">{analytics.correctAnswers} of {analytics.totalQuestions} questions correct</p>

                {/* Compact Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      analytics.overallScore >= 70
                        ? 'bg-gradient-to-r from-accent to-primary'
                        : 'bg-gradient-to-r from-red-400 to-red-500'
                    }`}
                    style={{ width: `${analytics.overallScore}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Quick Stats */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-accent/10 rounded-lg p-2">
                  <CheckCircle className="h-4 w-4 text-accent" />
                </div>
                <span className="text-sm text-grey">Correct</span>
              </div>
              <span className="text-xl font-bold text-accent">{analytics.correctAnswers}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-red-100 rounded-lg p-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                </div>
                <span className="text-sm text-grey">Incorrect</span>
              </div>
              <span className="text-xl font-bold text-red-500">{analytics.incorrectAnswers}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 rounded-lg p-2">
                  <Clock className="h-4 w-4 text-primary" />
                </div>
                <span className="text-sm text-grey">Avg. Time</span>
              </div>
              <span className="text-xl font-bold text-primary">{formatTime(analytics.averageTimePerQuestion)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance by Difficulty - Modern Cards */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
          <div className="bg-primary/10 rounded-xl p-2">
            <BarChart3 className="h-6 w-6 text-primary" />
          </div>
          Performance by Difficulty
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {Object.entries(analytics.difficultyPerformance).map(([difficulty, performance]) => {
            const accuracy = performance.total > 0 ? (performance.correct / performance.total) * 100 : 0;
            const difficultyColors = {
              Easy: { bg: "from-accent/10 to-accent/20", border: "border-accent/30", text: "text-accent", icon: "bg-accent/10" },
              Medium: { bg: "from-yellow-100 to-yellow-200", border: "border-yellow-300", text: "text-yellow-700", icon: "bg-yellow-100" },
              Hard: { bg: "from-red-100 to-red-200", border: "border-red-300", text: "text-red-600", icon: "bg-red-100" }
            };
            const colors = difficultyColors[difficulty as keyof typeof difficultyColors];

            return (
              <div key={difficulty} className={`bg-gradient-to-br ${colors.bg} rounded-3xl p-8 border ${colors.border} shadow-lg`}>
                <div className="text-center">
                  <div className={`${colors.icon} rounded-2xl p-4 inline-flex mb-6`}>
                    <Target className={`h-8 w-8 ${colors.text}`} />
                  </div>
                  <h4 className="text-xl font-bold text-charcoal mb-2">{difficulty}</h4>
                  <p className={`text-5xl font-bold ${colors.text} mb-2`}>{Math.round(accuracy)}%</p>
                  <p className="text-grey font-medium mb-4">{performance.correct} of {performance.total} correct</p>

                  {/* Progress Circle */}
                  <div className="relative w-24 h-24 mx-auto mb-4">
                    <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        className="text-gray-200"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        strokeDasharray={`${accuracy * 2.51} 251`}
                        className={colors.text}
                        strokeLinecap="round"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className={`text-lg font-bold ${colors.text}`}>{Math.round(accuracy)}%</span>
                    </div>
                  </div>

                  <p className="text-sm text-grey">Avg: {formatTime(performance.avgTime)}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Category Performance - Modern Graph */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
          <div className="bg-primary/10 rounded-xl p-2">
            <Target className="h-6 w-6 text-primary" />
          </div>
          Performance by Category
        </h3>

        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <div className="grid gap-6">
            {Object.entries(analytics.categoryPerformance).map(([category, performance], index) => {
              const accuracy = (performance.correct / performance.total) * 100;
              const isStrongest = category === analytics.strongestCategory;
              const isWeakest = category === analytics.weakestCategory;

              return (
                <div key={category} className="relative">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full ${
                        isStrongest ? 'bg-accent' :
                        isWeakest ? 'bg-red-500' :
                        'bg-primary'
                      }`}></div>
                      <span className="font-semibold text-charcoal text-lg">{category}</span>
                      {isStrongest && (
                        <span className="bg-accent text-white text-xs px-2 py-1 rounded-full font-medium">
                          Strongest
                        </span>
                      )}
                      {isWeakest && (
                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Needs Focus
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-charcoal">{Math.round(accuracy)}%</span>
                      <p className="text-sm text-grey">{performance.correct} of {performance.total}</p>
                    </div>
                  </div>

                  <div className="relative">
                    <div className="w-full bg-gray-200 rounded-full h-4 shadow-inner">
                      <div
                        className={`h-4 rounded-full transition-all duration-1000 ${
                          isStrongest ? 'bg-gradient-to-r from-accent to-primary' :
                          isWeakest ? 'bg-gradient-to-r from-red-400 to-red-500' :
                          accuracy >= 80 ? 'bg-gradient-to-r from-accent to-primary' :
                          accuracy >= 60 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                          'bg-gradient-to-r from-red-400 to-red-500'
                        }`}
                        style={{
                          width: `${accuracy}%`,
                          animationDelay: `${index * 200}ms`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Category Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 pt-8 border-t border-gray-200">
            <div className="bg-gradient-to-br from-accent/10 to-accent/20 rounded-2xl p-6 border border-accent/30">
              <div className="flex items-center gap-3 mb-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span className="font-semibold text-charcoal">Strongest Category</span>
              </div>
              <p className="text-xl font-bold text-accent">{analytics.strongestCategory}</p>
              <p className="text-sm text-grey">
                {Math.round((analytics.categoryPerformance[analytics.strongestCategory]?.correct / analytics.categoryPerformance[analytics.strongestCategory]?.total) * 100)}% accuracy
              </p>
            </div>

            <div className="bg-gradient-to-br from-red-100 to-red-200 rounded-2xl p-6 border border-red-300">
              <div className="flex items-center gap-3 mb-2">
                <Flag className="h-5 w-5 text-red-600" />
                <span className="font-semibold text-charcoal">Focus Area</span>
              </div>
              <p className="text-xl font-bold text-red-600">{analytics.weakestCategory}</p>
              <p className="text-sm text-grey">
                {Math.round((analytics.categoryPerformance[analytics.weakestCategory]?.correct / analytics.categoryPerformance[analytics.weakestCategory]?.total) * 100)}% accuracy
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Exam Momentum and Time Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Exam Momentum Graph */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
            <div className="bg-primary/10 rounded-xl p-2">
              <TrendingUp className="h-6 w-6 text-primary" />
            </div>
            Exam Momentum
          </h3>

          <div className="relative">
            <div className="flex items-end justify-between h-40 mb-6 bg-gray-50 rounded-2xl p-4 relative group">
              {analytics.examMomentum.slice(0, 15).map((point, index) => {
                // Calculate height based on time spent relative to average, with min/max bounds
                const maxTime = Math.max(...analytics.examMomentum.map(p => p.timeSpent));
                const heightPercentage = maxTime > 0 ? Math.max(20, Math.min(90, (point.timeSpent / maxTime) * 80)) : 30;
                const question = questions[point.questionIndex];

                return (
                  <div
                    key={index}
                    className={`w-6 rounded-t-lg transition-all duration-300 shadow-sm hover:scale-125 hover:shadow-xl cursor-pointer relative group ${
                      point.isCorrect ? "bg-gradient-to-t from-accent to-primary" : "bg-gradient-to-t from-red-400 to-red-500"
                    } ${hoveredQuestion === point.questionIndex ? 'scale-125 shadow-xl z-20' : ''}`}
                    style={{
                      height: `${heightPercentage}%`,
                      animationDelay: `${index * 100}ms`
                    }}
                    onMouseEnter={() => setHoveredQuestion(point.questionIndex)}
                    onMouseLeave={() => setHoveredQuestion(null)}
                    onClick={() => {
                      // Scroll to the question in the review section
                      const questionElement = document.getElementById(`question-${point.questionIndex}`);
                      if (questionElement) {
                        questionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        questionElement.classList.add('ring-4', 'ring-primary/30', 'ring-offset-2');
                        setTimeout(() => {
                          questionElement.classList.remove('ring-4', 'ring-primary/30', 'ring-offset-2');
                        }, 2000);
                      }
                    }}
                  >
                    {/* Enhanced Hover Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 opacity-0 group-hover:opacity-100 transition-all duration-300 z-30 pointer-events-none">
                      <div className="bg-white border border-gray-200 text-charcoal text-sm rounded-xl p-4 shadow-2xl min-w-64">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white ${
                              point.isCorrect ? 'bg-accent' : 'bg-red-500'
                            }`}>
                              {point.questionIndex + 1}
                            </div>
                            <p className="font-bold">Question {point.questionIndex + 1}</p>
                          </div>

                          <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                            point.isCorrect ? 'bg-accent/10 text-accent' : 'bg-red-100 text-red-600'
                          }`}>
                            {point.isCorrect ? "✓ Correct" : "✗ Incorrect"}
                          </div>

                          <div className="space-y-2 text-xs">
                            <div className="flex justify-between">
                              <span className="text-grey">Time Spent:</span>
                              <span className="font-semibold">{formatTime(point.timeSpent)}</span>
                            </div>
                            {question && (
                              <>
                                <div className="flex justify-between">
                                  <span className="text-grey">Topic:</span>
                                  <span className="font-semibold">{question.topic || 'General'}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-grey">Difficulty:</span>
                                  <span className={`font-semibold ${
                                    question.difficulty === 'Easy' ? 'text-accent' :
                                    question.difficulty === 'Hard' ? 'text-red-600' : 'text-yellow-600'
                                  }`}>{question.difficulty || 'Medium'}</span>
                                </div>
                              </>
                            )}
                          </div>

                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <p className="text-xs text-primary font-medium">Click to view question details</p>
                          </div>
                        </div>
                        {/* Arrow */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-6 border-transparent border-t-white drop-shadow-sm"></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="flex justify-between text-sm text-grey font-medium">
              <span>Question 1</span>
              <span>Question {Math.min(15, analytics.totalQuestions)}</span>
            </div>
            <div className="mt-6 flex items-center justify-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gradient-to-r from-accent to-primary rounded-full"></div>
                <span className="text-charcoal font-medium">Correct</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gradient-to-r from-red-400 to-red-500 rounded-full"></div>
                <span className="text-charcoal font-medium">Incorrect</span>
              </div>
              <span className="text-grey">Height = Time Spent</span>
            </div>
          </div>
        </div>

        {/* Time Analysis */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
            <div className="bg-primary/10 rounded-xl p-2">
              <Clock className="h-6 w-6 text-primary" />
            </div>
            Time Analysis
          </h3>

          <div className="space-y-8">
            <div className="text-center bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl p-6 border border-primary/30">
              <p className="text-4xl font-bold text-primary mb-2">{formatTime(attempt.timeSpentSeconds || 0)}</p>
              <p className="text-grey font-medium">Total Time Spent</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-6 bg-gradient-to-br from-accent/10 to-accent/20 rounded-2xl border border-accent/30">
                <p className="text-2xl font-bold text-accent mb-1">{formatTime(analytics.averageTimePerQuestion)}</p>
                <p className="text-sm text-grey font-medium">Avg per Question</p>
              </div>
              <div className="text-center p-6 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl border border-purple-300">
                <p className="text-2xl font-bold text-purple-600 mb-1">
                  {analytics.examMomentum.length > 0
                    ? formatTime(Math.max(...analytics.examMomentum.map(m => m.timeSpent)))
                    : formatTime(0)
                  }
                </p>
                <p className="text-sm text-grey font-medium">Longest Question</p>
              </div>
            </div>

            {/* Time by Difficulty */}
            <div className="bg-gray-50 rounded-2xl p-6">
              <h4 className="font-bold text-charcoal mb-4">Average Time by Difficulty</h4>
              <div className="space-y-3">
                {Object.entries(analytics.difficultyPerformance).map(([difficulty, performance]) => (
                  <div key={difficulty} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        difficulty === "Easy" ? "bg-accent" :
                        difficulty === "Medium" ? "bg-yellow-500" : "bg-red-500"
                      }`}></div>
                      <span className="font-medium text-charcoal">{difficulty}</span>
                    </div>
                    <span className="text-lg font-bold text-charcoal">
                      {formatTime(performance.avgTime)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Previous Attempts Comparison */}
      {previousAttempts.length > 0 && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
            <div className="bg-primary/10 rounded-xl p-2">
              <TrendingUp className="h-6 w-6 text-primary" />
            </div>
            Previous Attempts Comparison
          </h3>

          <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
            <div className="grid gap-6">
              {/* Current vs Previous Performance */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl p-6 border border-primary/30">
                  <h4 className="text-lg font-bold text-charcoal mb-4">Current Attempt</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-grey">Score</span>
                      <span className="text-2xl font-bold text-primary">{analytics.overallScore}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-grey">Time</span>
                      <span className="font-semibold text-charcoal">{formatTime(attempt.timeSpentSeconds || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-grey">Correct</span>
                      <span className="font-semibold text-charcoal">{analytics.correctAnswers}/{analytics.totalQuestions}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                  <h4 className="text-lg font-bold text-charcoal mb-4">Best Previous</h4>
                  {(() => {
                    const bestAttempt = previousAttempts.reduce((best, current) =>
                      (current.score || 0) > (best.score || 0) ? current : best
                    );
                    return (
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-grey">Score</span>
                          <span className="text-2xl font-bold text-accent">{bestAttempt.score || 0}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-grey">Time</span>
                          <span className="font-semibold text-charcoal">{formatTime(bestAttempt.timeSpentSeconds || 0)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-grey">Correct</span>
                          <span className="font-semibold text-charcoal">{bestAttempt.correctAnswers || 0}/{bestAttempt.totalQuestions || 0}</span>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>

              {/* Progress Chart */}
              <div>
                <h4 className="text-lg font-bold text-charcoal mb-4">Score Progress Over Time</h4>
                <div className="bg-gray-50 rounded-2xl p-6">
                  <div className="flex items-end justify-between h-32">
                    {[...previousAttempts.slice(-5), { score: analytics.overallScore, id: 'current' }].map((attemptData, index, array) => {
                      const isCurrentAttempt = attemptData.id === 'current';
                      const score = attemptData.score || 0;
                      const height = Math.max(20, (score / 100) * 80);

                      return (
                        <div key={attemptData.id} className="flex flex-col items-center">
                          <div
                            className={`w-8 rounded-t-lg transition-all duration-500 ${
                              isCurrentAttempt
                                ? 'bg-gradient-to-t from-primary to-accent shadow-lg'
                                : score >= 70
                                ? 'bg-gradient-to-t from-accent to-primary'
                                : 'bg-gradient-to-t from-red-400 to-red-500'
                            }`}
                            style={{
                              height: `${height}%`,
                              animationDelay: `${index * 200}ms`
                            }}
                            title={`${isCurrentAttempt ? 'Current' : `Attempt ${index + 1}`}: ${score}%`}
                          ></div>
                          <span className="text-xs text-grey mt-2">
                            {isCurrentAttempt ? 'Current' : `#${index + 1}`}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Improvement Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {(() => {
                  const lastAttempt = previousAttempts[previousAttempts.length - 1];
                  const scoreImprovement = lastAttempt ? analytics.overallScore - (lastAttempt.score || 0) : 0;
                  const timeImprovement = lastAttempt ? (lastAttempt.timeSpentSeconds || 0) - (attempt.timeSpentSeconds || 0) : 0;
                  const accuracyImprovement = lastAttempt ? analytics.correctAnswers - (lastAttempt.correctAnswers || 0) : 0;

                  return (
                    <>
                      <div className={`p-4 rounded-xl border ${scoreImprovement >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center gap-2 mb-1">
                          <TrendingUp className={`h-4 w-4 ${scoreImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                          <span className="text-sm font-medium text-charcoal">Score Change</span>
                        </div>
                        <p className={`text-lg font-bold ${scoreImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {scoreImprovement >= 0 ? '+' : ''}{scoreImprovement.toFixed(1)}%
                        </p>
                      </div>

                      <div className={`p-4 rounded-xl border ${timeImprovement >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center gap-2 mb-1">
                          <Clock className={`h-4 w-4 ${timeImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                          <span className="text-sm font-medium text-charcoal">Time Change</span>
                        </div>
                        <p className={`text-lg font-bold ${timeImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {timeImprovement >= 0 ? '+' : ''}{formatTime(Math.abs(timeImprovement))}
                        </p>
                      </div>

                      <div className={`p-4 rounded-xl border ${accuracyImprovement >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center gap-2 mb-1">
                          <Target className={`h-4 w-4 ${accuracyImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                          <span className="text-sm font-medium text-charcoal">Accuracy Change</span>
                        </div>
                        <p className={`text-lg font-bold ${accuracyImprovement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {accuracyImprovement >= 0 ? '+' : ''}{accuracyImprovement} questions
                        </p>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed Question Review */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold text-charcoal mb-8 flex items-center gap-3">
          <div className="bg-primary/10 rounded-xl p-2">
            <BookOpen className="h-6 w-6 text-primary" />
          </div>
          Question Review
        </h3>

        <div className="bg-gradient-to-br from-white to-muted/30 rounded-3xl p-8 shadow-xl border border-gray-100">
          <div className="space-y-6">
            {questions.map((question, index) => {
              const answer = attempt.answers.find(a => a.questionId === question.id);
              const isAnswered = !!answer;
              const isCorrect = answer?.isCorrect || false;
              const timeSpent = answer?.timeSpent || 0;

              return (
                <div
                  key={question.id}
                  id={`question-${index}`}
                  className={`border rounded-2xl p-6 transition-all hover:shadow-md scroll-mt-24 ${
                    !isAnswered
                      ? 'border-gray-300 bg-gray-50'
                      : isCorrect
                      ? 'border-accent/30 bg-accent/5'
                      : 'border-red-300 bg-red-50'
                  }`}
                >
                  <div className="flex items-start gap-4">
                    {/* Question Number & Status */}
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg ${
                        !isAnswered
                          ? 'bg-gray-200 text-gray-600'
                          : isCorrect
                          ? 'bg-accent text-white'
                          : 'bg-red-500 text-white'
                      }`}>
                        {index + 1}
                      </div>
                    </div>

                    {/* Question Content */}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <h4 className="text-lg font-semibold text-charcoal">Question {index + 1}</h4>
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                          !isAnswered
                            ? 'bg-gray-200 text-gray-600'
                            : isCorrect
                            ? 'bg-accent text-white'
                            : 'bg-red-500 text-white'
                        }`}>
                          {!isAnswered ? 'Not Answered' : isCorrect ? 'Correct' : 'Incorrect'}
                        </div>
                        {question.difficulty && (
                          <div className={`px-2 py-1 rounded-md text-xs font-medium ${
                            question.difficulty === 'Easy' ? 'bg-accent/20 text-accent' :
                            question.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {question.difficulty}
                          </div>
                        )}
                        {question.topic && (
                          <div className="px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                            {question.topic}
                          </div>
                        )}
                      </div>

                      {/* Question Text */}
                      <div className="mb-4">
                        <p className="text-charcoal leading-relaxed">{question.question}</p>
                      </div>

                      {/* Answer Options */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                        {(['A', 'B', 'C', 'D'] as const).map((option) => {
                          const isSelected = answer?.selectedAnswer === option;
                          const isCorrectAnswer = question.correct === option;

                          return (
                            <div
                              key={option}
                              className={`p-3 rounded-lg border transition-all ${
                                isCorrectAnswer
                                  ? 'border-accent bg-accent/10 text-accent'
                                  : isSelected && !isCorrect
                                  ? 'border-red-300 bg-red-50 text-red-700'
                                  : 'border-gray-200 bg-white text-charcoal'
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold ${
                                  isCorrectAnswer
                                    ? 'bg-accent text-white'
                                    : isSelected && !isCorrect
                                    ? 'bg-red-500 text-white'
                                    : 'bg-gray-200 text-gray-600'
                                }`}>
                                  {option}
                                </div>
                                <span className="flex-1">{question.options[option]}</span>
                                {isCorrectAnswer && (
                                  <CheckCircle className="h-5 w-5 text-accent" />
                                )}
                                {isSelected && !isCorrect && (
                                  <XCircle className="h-5 w-5 text-red-500" />
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {/* Question Stats */}
                      <div className="flex items-center gap-6 text-sm text-grey">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          <span>Time: {timeSpent > 0 ? formatTime(timeSpent) : 'N/A'}</span>
                        </div>
                        {answer && (
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            <span>Your Answer: {answer.selectedAnswer}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          <span>Correct Answer: {question.correct}</span>
                        </div>
                      </div>

                      {/* Explanation if available */}
                      {question.explanation && (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <h5 className="font-semibold text-blue-800 mb-2">Explanation:</h5>
                          <p className="text-blue-700 text-sm leading-relaxed">{question.explanation}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
