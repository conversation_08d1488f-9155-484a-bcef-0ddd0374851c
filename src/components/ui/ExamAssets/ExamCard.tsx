"use client";

import Image from "next/image";
import { Card } from "@/components/ui/Card";
import { buttonVariants } from "@/components/ui/button";
import { dtcAssets } from "@/lib/assets";
import { Clock, CheckCircle, Brain, Timer, Shield, BookOpen, Zap, Target } from "lucide-react";

export type ExamMode = "practice" | "realExam" | "questionBank";

export interface ExamCardProps {
  mode: ExamMode;
  onModeSelect: (mode: ExamMode) => void;
  className?: string;
}

const examModeConfig = {
  practice: {
    title: "Practice Mode",
    image: dtcAssets.practiceMode,
    indicator: "PRACTICE",
    indicatorColor: "bg-accent text-white",
    icon: Brain,
    rules: [
      "No Time Limit",
      "Get Immediate Feedback after Question Answering",
      "Access to AI Analyzer"
    ]
  },
  realExam: {
    title: "Real Exam Mode",
    image: dtcAssets.realExamMode,
    indicator: "REAL EXAM",
    indicatorColor: "bg-red-500 text-white",
    icon: Timer,
    rules: [
      "Real Exam Mimic",
      "No Feedback except after end of Question",
      "Timer",
      "NO Browser Leave Monitoring"
    ]
  },
  questionBank: {
    title: "Question Bank Answering",
    image: dtcAssets.questionBankMode,
    indicator: "QUESTION BANK",
    indicatorColor: "bg-primary text-white",
    icon: BookOpen,
    rules: [
      "Answer All Questions From Beginning to End of Question Bank",
      "No Time Limit",
      "Immediate Feedback",
      "AI Analyzer"
    ]
  }
};

export function ExamCard({ mode, onModeSelect, className }: ExamCardProps) {
  const config = examModeConfig[mode];
  const IconComponent = config.icon;

  return (
    <div
      className={`group block cursor-pointer rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 ${className || ""}`}
      onClick={() => onModeSelect(mode)}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          onModeSelect(mode);
        }
      }}
      aria-label={`Select ${config.title}`}
    >
      <Card className="relative aspect-square w-full overflow-hidden rounded-2xl border border-gray-100 p-0 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-md">
        {/* Background Image */}
        <Image
          src={config.image}
          alt={`${config.title} background`}
          fill
          className="object-cover transition-transform duration-500 ease-out group-hover:scale-[1.02]"
          sizes="(min-width: 1024px) 25vw, (min-width: 640px) 50vw, 100vw"
          priority={false}
        />
        
        {/* Fade layer */}
        <div className="pointer-events-none absolute inset-0 z-[1] bg-black/50 transition-colors duration-300 group-hover:bg-black/30" />

        {/* Mode Indicator Badge */}
        <div className="absolute left-4 top-4 z-[3]">
          <div className={`flex items-center gap-2 rounded-full px-3 py-1.5 text-xs font-semibold uppercase tracking-wider ${config.indicatorColor} shadow-sm`}>
            <IconComponent className="h-3 w-3" />
            {config.indicator}
          </div>
        </div>

        {/* Main Content */}
        <div className="absolute inset-0 z-[2] flex flex-col items-center justify-center px-6 text-center">
          <h3 className="text-2xl font-semibold tracking-tight text-white drop-shadow-md md:text-3xl">
            {config.title}
          </h3>
          <span
            className={`${buttonVariants({ size: "lg" })} mt-4 h-11 rounded-md bg-white/90 px-8 text-base text-charcoal shadow-sm backdrop-blur-sm transition-colors hover:bg-white`}
          >
            Start
          </span>
        </div>

        {/* Rules Overlay - Shows on Hover */}
        <div className="absolute inset-0 z-[4] flex flex-col items-center justify-center bg-black/80 px-6 py-8 text-center opacity-0 transition-opacity duration-300 ease-out group-hover:opacity-100">
          <div className="mb-4">
            <IconComponent className="mx-auto h-8 w-8 text-white mb-2" />
            <h4 className="text-xl font-semibold text-white mb-4">{config.title} Rules</h4>
          </div>
          
          <div className="space-y-3 text-left">
            {config.rules.map((rule, index) => (
              <div key={index} className="flex items-start gap-3 text-white">
                <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0 text-accent" />
                <span className="text-sm leading-relaxed">{rule}</span>
              </div>
            ))}
          </div>

          <div className="mt-6">
            <span
              className={`${buttonVariants({ size: "lg" })} h-11 rounded-md bg-white px-8 text-base text-charcoal shadow-sm transition-colors hover:bg-white/90`}
            >
              Select Mode
            </span>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default ExamCard;
