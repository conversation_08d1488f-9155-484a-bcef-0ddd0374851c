"use client";

import { type QuestionRecord } from "@/Services/questionsService";
import { CheckCircle, XCircle } from "lucide-react";

interface QuestionDisplayProps {
  question: QuestionRecord;
  selectedAnswer: "A" | "B" | "C" | "D" | null;
  onAnswerSelect: (answer: "A" | "B" | "C" | "D") => void;
  showCorrectAnswer: boolean;
  isAnswered: boolean;
}

export default function QuestionDisplay({
  question,
  selectedAnswer,
  onAnswerSelect,
  showCorrectAnswer,
  isAnswered,
}: QuestionDisplayProps) {
  const choices = [
    { key: "A" as const, text: question.choices[0] },
    { key: "B" as const, text: question.choices[1] },
    { key: "C" as const, text: question.choices[2] },
    { key: "D" as const, text: question.choices[3] },
  ];

  const getChoiceStyle = (choiceKey: "A" | "B" | "C" | "D") => {
    const isSelected = selectedAnswer === choiceKey;
    const isCorrect = question.correct === choiceKey;
    const isWrong = showCorrectAnswer && isSelected && !isCorrect;
    const shouldShowCorrect = showCorrectAnswer && isCorrect;

    let baseStyle = "flex items-start gap-4 p-4 rounded-lg border-2 cursor-pointer transition-all ";

    if (isWrong) {
      return baseStyle + "border-red-300 bg-red-50 text-red-800";
    }
    
    if (shouldShowCorrect) {
      return baseStyle + "border-green-300 bg-green-50 text-green-800";
    }
    
    if (isSelected) {
      return baseStyle + "border-primary bg-primary/5 text-primary";
    }
    
    if (isAnswered && !showCorrectAnswer) {
      return baseStyle + "border-gray-200 bg-gray-50 text-grey cursor-not-allowed";
    }
    
    return baseStyle + "border-gray-200 bg-white text-charcoal hover:border-primary/50 hover:bg-primary/5";
  };

  const getChoiceIcon = (choiceKey: "A" | "B" | "C" | "D") => {
    const isSelected = selectedAnswer === choiceKey;
    const isCorrect = question.correct === choiceKey;
    const isWrong = showCorrectAnswer && isSelected && !isCorrect;
    const shouldShowCorrect = showCorrectAnswer && isCorrect;

    if (isWrong) {
      return <XCircle className="h-5 w-5 text-red-600 mt-0.5" />;
    }
    
    if (shouldShowCorrect) {
      return <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />;
    }
    
    return (
      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-semibold mt-0.5 ${
        isSelected 
          ? "border-primary bg-primary text-white" 
          : "border-gray-300 bg-white text-grey"
      }`}>
        {choiceKey}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Question Text */}
      <div className="space-y-4">
        <div className="flex items-start gap-3">
          <div className="bg-primary/10 rounded-lg p-2 mt-1">
            <span className="text-primary font-semibold text-sm">Q</span>
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-charcoal leading-relaxed">
              {question.question}
            </h2>
          </div>
        </div>
      </div>

      {/* Answer Choices */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-grey uppercase tracking-wide">
          Select your answer:
        </h3>
        
        <div className="grid gap-3">
          {choices.map((choice) => (
            <div
              key={choice.key}
              onClick={() => !isAnswered && onAnswerSelect(choice.key)}
              className={getChoiceStyle(choice.key)}
            >
              {getChoiceIcon(choice.key)}
              <div className="flex-1">
                <p className="text-base leading-relaxed">{choice.text}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Show correct answer if applicable */}
      {showCorrectAnswer && (
        <div className="pt-4 border-t border-gray-100">
          <div className="text-sm text-right">
            <span className="text-grey">Correct Answer: </span>
            <span className="font-semibold text-green-700">{question.correct}</span>
          </div>
        </div>
      )}
    </div>
  );
}
