"use client";

import { useMemo } from "react";

interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

interface SimpleChartProps {
  data: ChartDataPoint[];
  type: "bar" | "pie" | "line";
  height?: number;
  className?: string;
}

export function SimpleChart({ data, type, height = 200, className = "" }: SimpleChartProps) {
  const maxValue = useMemo(() => Math.max(...data.map(d => d.value)), [data]);

  if (type === "bar") {
    return (
      <div className={`w-full ${className}`} style={{ height }}>
        <div className="flex items-end justify-between h-full gap-2">
          {data.map((item, index) => (
            <div key={index} className="flex flex-col items-center flex-1">
              <div
                className={`w-full rounded-t transition-all duration-500 ${
                  item.color || "bg-primary"
                }`}
                style={{
                  height: `${(item.value / maxValue) * 80}%`,
                  minHeight: "4px",
                }}
                title={`${item.label}: ${item.value}`}
              ></div>
              <span className="text-xs text-grey mt-2 text-center">{item.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (type === "pie") {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <div className="relative">
          <svg width={height * 0.8} height={height * 0.8} className="transform -rotate-90">
            {data.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const angle = (item.value / total) * 360;
              const radius = (height * 0.8) / 2 - 20;
              const centerX = (height * 0.8) / 2;
              const centerY = (height * 0.8) / 2;
              
              const startAngle = currentAngle;
              const endAngle = currentAngle + angle;
              currentAngle += angle;

              const startX = centerX + radius * Math.cos((startAngle * Math.PI) / 180);
              const startY = centerY + radius * Math.sin((startAngle * Math.PI) / 180);
              const endX = centerX + radius * Math.cos((endAngle * Math.PI) / 180);
              const endY = centerY + radius * Math.sin((endAngle * Math.PI) / 180);

              const largeArcFlag = angle > 180 ? 1 : 0;

              const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${startX} ${startY}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
                "Z",
              ].join(" ");

              const colors = [
                "fill-green-400",
                "fill-blue-400", 
                "fill-yellow-400",
                "fill-red-400",
                "fill-purple-400",
                "fill-pink-400",
              ];

              return (
                <path
                  key={index}
                  d={pathData}
                  className={item.color || colors[index % colors.length]}
                  stroke="white"
                  strokeWidth="2"
                />
              );
            })}
          </svg>
          
          {/* Legend */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4">
            <div className="flex flex-wrap justify-center gap-2">
              {data.map((item, index) => {
                const colors = [
                  "bg-green-400",
                  "bg-blue-400", 
                  "bg-yellow-400",
                  "bg-red-400",
                  "bg-purple-400",
                  "bg-pink-400",
                ];
                
                return (
                  <div key={index} className="flex items-center gap-1">
                    <div className={`w-3 h-3 rounded ${item.color || colors[index % colors.length]}`}></div>
                    <span className="text-xs text-grey">{item.label}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (type === "line") {
    const width = 300;
    const chartHeight = height - 40;
    
    return (
      <div className={`w-full ${className}`} style={{ height }}>
        <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`}>
          {/* Grid lines */}
          {[0, 25, 50, 75, 100].map((y) => (
            <line
              key={y}
              x1="40"
              y1={20 + (chartHeight * y) / 100}
              x2={width - 20}
              y2={20 + (chartHeight * y) / 100}
              stroke="#e5e7eb"
              strokeWidth="1"
            />
          ))}
          
          {/* Data line */}
          <polyline
            fill="none"
            stroke="#3b82f6"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            points={data
              .map((item, index) => {
                const x = 40 + ((width - 60) * index) / (data.length - 1);
                const y = 20 + chartHeight - (chartHeight * item.value) / maxValue;
                return `${x},${y}`;
              })
              .join(" ")}
          />
          
          {/* Data points */}
          {data.map((item, index) => {
            const x = 40 + ((width - 60) * index) / (data.length - 1);
            const y = 20 + chartHeight - (chartHeight * item.value) / maxValue;
            
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="4"
                fill="#3b82f6"
                stroke="white"
                strokeWidth="2"
              />
            );
          })}
          
          {/* X-axis labels */}
          {data.map((item, index) => {
            const x = 40 + ((width - 60) * index) / (data.length - 1);
            
            return (
              <text
                key={index}
                x={x}
                y={height - 5}
                textAnchor="middle"
                className="text-xs fill-grey"
              >
                {item.label}
              </text>
            );
          })}
        </svg>
      </div>
    );
  }

  return null;
}
