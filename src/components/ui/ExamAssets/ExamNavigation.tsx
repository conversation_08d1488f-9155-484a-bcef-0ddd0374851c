"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ExamNavigationProps {
  currentQuestionIndex: number;
  totalQuestions: number;
  onQuestionSelect: (index: number) => void;
  getQuestionStatus: (index: number) => "answered" | "unanswered" | "correct" | "incorrect" | "flagged";
}

export default function ExamNavigation({
  currentQuestionIndex,
  totalQuestions,
  onQuestionSelect,
  getQuestionStatus,
}: ExamNavigationProps) {
  // Calculate the range of questions to show (current ± 5)
  const getVisibleRange = () => {
    const start = Math.max(0, currentQuestionIndex - 5);
    const end = Math.min(totalQuestions - 1, currentQuestionIndex + 5);
    return { start, end };
  };

  const { start, end } = getVisibleRange();
  const visibleQuestions = Array.from({ length: end - start + 1 }, (_, i) => start + i);

  const getQuestionButtonStyle = (index: number) => {
    const status = getQuestionStatus(index);
    const isCurrent = index === currentQuestionIndex;
    
    let baseStyle = "w-10 h-10 rounded-lg text-sm font-medium transition-all ";
    
    if (isCurrent) {
      baseStyle += "ring-2 ring-primary ring-offset-2 ";
    }
    
    switch (status) {
      case "correct":
        return baseStyle + (isCurrent 
          ? "bg-green-600 text-white" 
          : "bg-green-100 text-green-700 hover:bg-green-200");
      case "incorrect":
        return baseStyle + (isCurrent 
          ? "bg-red-600 text-white" 
          : "bg-red-100 text-red-700 hover:bg-red-200");
      case "answered":
        return baseStyle + (isCurrent 
          ? "bg-blue-600 text-white" 
          : "bg-blue-100 text-blue-700 hover:bg-blue-200");
      case "flagged":
        return baseStyle + (isCurrent 
          ? "bg-yellow-600 text-white" 
          : "bg-yellow-100 text-yellow-700 hover:bg-yellow-200");
      default:
        return baseStyle + (isCurrent 
          ? "bg-primary text-white" 
          : "bg-gray-100 text-grey hover:bg-gray-200");
    }
  };

  const canScrollLeft = start > 0;
  const canScrollRight = end < totalQuestions - 1;

  const scrollLeft = () => {
    const newIndex = Math.max(0, currentQuestionIndex - 6);
    onQuestionSelect(newIndex);
  };

  const scrollRight = () => {
    const newIndex = Math.min(totalQuestions - 1, currentQuestionIndex + 6);
    onQuestionSelect(newIndex);
  };

  return (
    <div className="flex items-center justify-center gap-2">
      {/* Left scroll button */}
      {canScrollLeft && (
        <Button
          onClick={scrollLeft}
          variant="outline"
          size="sm"
          className="w-8 h-8 p-0 bg-white/10 border-white/20 text-white hover:bg-white/20"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      )}

      {/* Question number buttons */}
      <div className="flex items-center gap-1">
        {visibleQuestions.map((questionIndex) => (
          <button
            key={questionIndex}
            onClick={() => onQuestionSelect(questionIndex)}
            className={getQuestionButtonStyle(questionIndex)}
            title={`Question ${questionIndex + 1} - ${getQuestionStatus(questionIndex)}`}
          >
            {questionIndex + 1}
          </button>
        ))}
      </div>

      {/* Right scroll button */}
      {canScrollRight && (
        <Button
          onClick={scrollRight}
          variant="outline"
          size="sm"
          className="w-8 h-8 p-0 bg-white/10 border-white/20 text-white hover:bg-white/20"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      )}

      {/* Legend */}
      <div className="ml-6 flex items-center gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-green-100 rounded"></div>
          <span className="text-white/80">Correct</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-red-100 rounded"></div>
          <span className="text-white/80">Incorrect</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-yellow-100 rounded"></div>
          <span className="text-white/80">Flagged</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-gray-100 rounded"></div>
          <span className="text-white/80">Unanswered</span>
        </div>
      </div>
    </div>
  );
}
