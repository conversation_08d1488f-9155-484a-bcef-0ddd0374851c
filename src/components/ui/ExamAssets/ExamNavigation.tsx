"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ExamNavigationProps {
  currentQuestionIndex: number;
  totalQuestions: number;
  onQuestionSelect: (index: number) => void;
  getQuestionStatus: (index: number) => "answered" | "unanswered" | "correct" | "incorrect" | "flagged";
}

export default function ExamNavigation({
  currentQuestionIndex,
  totalQuestions,
  onQuestionSelect,
  getQuestionStatus,
}: ExamNavigationProps) {
  // Calculate the range of questions to show (current ± 5)
  const getVisibleRange = () => {
    const start = Math.max(0, currentQuestionIndex - 5);
    const end = Math.min(totalQuestions - 1, currentQuestionIndex + 5);
    return { start, end };
  };

  const { start, end } = getVisibleRange();
  const visibleQuestions = Array.from({ length: end - start + 1 }, (_, i) => start + i);

  const getQuestionButtonStyle = (index: number) => {
    const status = getQuestionStatus(index);
    const isCurrent = index === currentQuestionIndex;

    let baseStyle = "w-12 h-12 rounded-xl text-sm font-semibold transition-all duration-200 shadow-sm ";

    if (isCurrent) {
      baseStyle += "ring-2 ring-white ring-offset-2 ring-offset-transparent scale-110 ";
    }

    switch (status) {
      case "correct":
        return baseStyle + (isCurrent
          ? "bg-green-500 text-white shadow-lg"
          : "bg-green-100 text-green-700 hover:bg-green-200 hover:scale-105");
      case "incorrect":
        return baseStyle + (isCurrent
          ? "bg-red-500 text-white shadow-lg"
          : "bg-red-100 text-red-700 hover:bg-red-200 hover:scale-105");
      case "answered":
        return baseStyle + (isCurrent
          ? "bg-blue-500 text-white shadow-lg"
          : "bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105");
      case "flagged":
        return baseStyle + (isCurrent
          ? "bg-yellow-500 text-white shadow-lg"
          : "bg-yellow-100 text-yellow-700 hover:bg-yellow-200 hover:scale-105");
      default:
        return baseStyle + (isCurrent
          ? "bg-white text-primary shadow-lg"
          : "bg-white/80 text-charcoal hover:bg-white hover:scale-105");
    }
  };

  const canScrollLeft = start > 0;
  const canScrollRight = end < totalQuestions - 1;

  const scrollLeft = () => {
    const newIndex = Math.max(0, currentQuestionIndex - 6);
    onQuestionSelect(newIndex);
  };

  const scrollRight = () => {
    const newIndex = Math.min(totalQuestions - 1, currentQuestionIndex + 6);
    onQuestionSelect(newIndex);
  };

  return (
    <div className="space-y-4">
      {/* Question Navigation Buttons */}
      <div className="flex items-center justify-center gap-3">
        {/* Left scroll button */}
        {canScrollLeft && (
          <Button
            onClick={scrollLeft}
            variant="outline"
            size="sm"
            className="w-10 h-10 p-0 bg-white/10 border-white/20 text-white hover:bg-white/20 rounded-lg transition-all"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        )}

        {/* Question number buttons */}
        <div className="flex items-center gap-2">
          {visibleQuestions.map((questionIndex) => (
            <button
              key={questionIndex}
              onClick={() => onQuestionSelect(questionIndex)}
              className={getQuestionButtonStyle(questionIndex)}
              title={`Question ${questionIndex + 1} - ${getQuestionStatus(questionIndex)}`}
            >
              {questionIndex + 1}
            </button>
          ))}
        </div>

        {/* Right scroll button */}
        {canScrollRight && (
          <Button
            onClick={scrollRight}
            variant="outline"
            size="sm"
            className="w-10 h-10 p-0 bg-white/10 border-white/20 text-white hover:bg-white/20 rounded-lg transition-all"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center gap-6 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-100 rounded-full border border-green-200"></div>
          <span className="text-white/80 font-medium">Correct</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-100 rounded-full border border-red-200"></div>
          <span className="text-white/80 font-medium">Incorrect</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-yellow-100 rounded-full border border-yellow-200"></div>
          <span className="text-white/80 font-medium">Flagged</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-gray-100 rounded-full border border-gray-200"></div>
          <span className="text-white/80 font-medium">Unanswered</span>
        </div>
      </div>
    </div>
  );
}
