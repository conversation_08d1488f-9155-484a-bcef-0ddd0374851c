"use client";
import { useState } from "react";
import { emailPasswordSignUp, emailPasswordSignIn } from "@/Services/authService";

function getErrorMessage(e: unknown): string {
  if (e instanceof Error) return e.message;
  if (typeof e === "string") return e;
  try {
    return JSON.stringify(e);
  } catch {
    return "Failed";
  }
}

export default function SetupGate({ children }: { children: React.ReactNode }) {
  const enableSetup = process.env.NEXT_PUBLIC_ENABLE_SETUP === "true";
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!enableSetup) return children;

  async function handleCreate() {
    try {
      setLoading(true);
      setError(null);
      await emailPasswordSignUp({ email, password });
    } catch (e: unknown) {
      setError(getErrorMessage(e));
    } finally {
      setLoading(false);
    }
  }

  async function handleSignIn() {
    try {
      setLoading(true);
      setError(null);
      await emailPasswordSignIn({ email, password });
    } catch (e: unknown) {
      setError(getErrorMessage(e));
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen grid place-items-center p-6">
      <div className="w-full max-w-md bg-white rounded-xl border border-gray-200 shadow-sm p-6 space-y-4">
        <h1 className="text-xl font-semibold">Initial Setup</h1>
        <p className="text-sm text-gray-600">Create or sign in a first user. Disable this screen by setting NEXT_PUBLIC_ENABLE_SETUP=false.</p>
        <div className="space-y-3">
          <input
            placeholder="Email"
            className="w-full border border-gray-200 rounded-lg px-3 py-2"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <input
            placeholder="Password"
            type="password"
            className="w-full border border-gray-200 rounded-lg px-3 py-2"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
        </div>
        {error && <div className="text-sm text-red-600">{error}</div>}
        <div className="flex gap-2">
          <button onClick={handleCreate} disabled={loading} className="px-4 py-2 rounded-lg bg-gray-900 text-white">
            {loading ? "..." : "Create user"}
          </button>
          <button onClick={handleSignIn} disabled={loading} className="px-4 py-2 rounded-lg border border-gray-300">
            {loading ? "..." : "Sign in"}
          </button>
        </div>
      </div>
    </div>
  );
}


