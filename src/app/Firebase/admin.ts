import type { App } from 'firebase-admin/app';
import type { Firestore } from 'firebase-admin/firestore';
import type { Auth } from 'firebase-admin/auth';

type FirebaseAdmin = {
  app: App;
  db: Firestore;
  auth: Auth;
};

let cached: FirebaseAdmin | undefined;

export async function getFirebaseAdmin(): Promise<FirebaseAdmin> {
  if (cached) return cached;

  const projectId = process.env.FIREBASE_PROJECT_ID;
  const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
  const rawPrivateKey = process.env.FIREBASE_PRIVATE_KEY;
  if (!projectId || !clientEmail || !rawPrivateKey) {
    throw new Error('Missing Firebase Admin credentials. Please set FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY.');
  }

  const { getApps, initializeApp, getApp, cert } = await import('firebase-admin/app');
  const { getFirestore } = await import('firebase-admin/firestore');
  const { getAuth } = await import('firebase-admin/auth');

  // Allow multiline keys provided with \n in env
  const privateKey = rawPrivateKey.replace(/\\n/g, '\n');

  const app = getApps().length
    ? getApp()
    : initializeApp({
        credential: cert({ projectId, clientEmail, privateKey }),
      });

  const db = getFirestore(app);
  const auth = getAuth(app);
  cached = { app, db, auth };
  return cached;
}


