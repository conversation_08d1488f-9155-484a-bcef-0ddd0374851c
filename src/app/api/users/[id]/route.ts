import { NextResponse } from 'next/server';
import { getFirebaseAdmin } from '@/app/Firebase/admin';

// DELETE /api/users/[id] - Delete a user
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { db, auth } = await getFirebaseAdmin();

    const ref = db.collection('users').doc(id);
    const snap = await ref.get();
    if (!snap.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Delete Firestore profile
    await ref.delete();

    // Attempt to delete Firebase Auth user as well
    try {
      await auth.deleteUser(id);
    } catch (err: unknown) {
      // If user not found in Auth or other non-fatal errors, log and continue
      console.warn('Auth delete warning:', err);
    }

    return NextResponse.json({ message: 'User deleted successfully from database' }, { status: 200 });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
