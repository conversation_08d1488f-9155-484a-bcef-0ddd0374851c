import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
// Rate limiting removed per user request

const SuggestSchema = z.object({
  certificate: z.object({ id: z.string(), name: z.string(), provider: z.string(), description: z.string() }),
  samples: z.array(z.object({ id: z.string(), question: z.string().min(2) })).min(1),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const parsed = SuggestSchema.safeParse(body);
    if (!parsed.success) {
      return Response.json({ error: "Invalid payload", details: parsed.error.flatten() }, { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({ error: "Missing GOOGLE_GENERATIVE_AI_API_KEY" }, { status: 500 });
    }
    const { certificate, samples } = parsed.data;
    const prompt = [
      `You are a senior exam taxonomy designer. Based on the certificate context and sample questions, suggest concise, business-friendly group names (3-8 groups).`,
      `Certificate: ${certificate.name} by ${certificate.provider}.`,
      `Description: ${certificate.description}`,
      `Samples:`,
      ...samples.map((s) => `- [${s.id}] ${s.question}`),
      `Return JSON { groups: string[] } with unique, short group names suitable as tags.`,
    ].join("\n");

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      schema: z.object({ groups: z.array(z.string()).min(1) }),
      prompt,
    });

    return Response.json({ ...result.object, usage: result.usage });
  } catch (e: any) {
    return Response.json({ error: e?.message ?? "Server error" }, { status: 500 });
  }
}


