import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
// Rate limiting removed per user request

const CategorizeSchema = z.object({
  certificate: z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    description: z.string(),
  }),
  mode: z.enum(["topic", "difficulty", "topic_and_difficulty"]),
  questionItems: z
    .array(z.object({ id: z.string(), question: z.string().min(2) }))
    .min(1),
  selectedQuestionIds: z.array(z.string()).min(1),
  customGroups: z.array(z.string()).optional(),
  allowedGroups: z.array(z.string()).optional(),
  maxTags: z.number().int().positive().max(5).optional(),
  strict: z.boolean().optional(),
});

export async function POST(req: NextRequest) {
  try {
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({ error: "Missing GOOGLE_GENERATIVE_AI_API_KEY" }, { status: 500 });
    }

    const body = await req.json();
    const parsed = CategorizeSchema.safeParse(body);
    if (!parsed.success) {
      return Response.json({ error: "Invalid payload", details: parsed.error.flatten() }, { status: 400 });
    }
    const { certificate, mode, questionItems, customGroups, allowedGroups = [], maxTags = 1, strict = true } = parsed.data;

    const systemPrompt = buildSystemPrompt({ certificate, mode, customGroups, allowedGroups, maxTags, strict });
    const userPrompt = buildUserPrompt(questionItems, mode);

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      // For more complex reasoning, switch to thinking model per endpoint logic
      schema: z.object({
        updates: z.array(
          z.object({
            id: z.string(),
            tags: z.array(z.string()).default([]),
            topic: z.string().optional(),
            difficulty: z.enum(["Easy", "Medium", "Hard"]).optional(),
          })
        ),
      }),
      prompt: `${systemPrompt}\n\n${userPrompt}`,
    });
    let updates = result.object.updates ?? [];
    if (strict && allowedGroups.length > 0) {
      const allowed = new Set<string>(allowedGroups.map((g) => g.trim().toLowerCase()));
      updates = updates.map((u: { id: string; tags?: string[]; topic?: string; difficulty?: string }) => {
        const intersect = (u.tags ?? []).filter((t: string) => allowed.has(String(t).trim().toLowerCase()));
        const clamped = intersect.slice(0, Math.max(1, Math.min(5, maxTags)));
        return { ...u, tags: clamped, topic: clamped[0] ?? u.topic };
      });
    }
    return Response.json({ updates, usage: result.usage });
  } catch (e: any) {
    return Response.json({ error: e?.message ?? "Server error" }, { status: 500 });
  }
}

function buildSystemPrompt(args: {
  certificate: { id: string; name: string; provider: string; description: string };
  mode: "topic" | "difficulty" | "topic_and_difficulty";
  customGroups?: string[];
  allowedGroups?: string[];
  maxTags?: number;
  strict?: boolean;
}) {
  const { certificate, mode, customGroups, allowedGroups = [], maxTags = 1, strict = true } = args;
  const groupHint = customGroups && customGroups.length ? `Use these group names when appropriate: ${customGroups.join(", ")}.` : "";
  const strictHint = (strict && allowedGroups.length)
    ? `You MUST ONLY use categories from this approved list: [${allowedGroups.join(", ")}]. Do not invent new categories. Use at most ${maxTags} tag(s) per question.`
    : `Prefer concise, reusable categories. Limit to at most ${maxTags} tag(s) per question.`;
  return [
    `You are an expert exam content classifier for enterprise data management and architecture.
Understand the certificate context and classify questions elegantly with consistent, business-friendly tags.
Always be precise and avoid hallucinations.`,
    `Certificate: ${certificate.name} by ${certificate.provider}.`,
    `Description: ${certificate.description}`,
    `Mode: ${mode}. ${groupHint}`,
    strictHint,
    `Output JSON with an 'updates' array: each { id, tags[], topic?, difficulty? }. Tags should include the chosen topic/difficulty labels.`,
  ].join("\n");
}

function buildUserPrompt(items: { id: string; question: string }[], mode: string) {
  const header = `Classify the following questions by ${mode}.`;
  const list = items.map((q, i) => `${i + 1}. [${q.id}] ${q.question}`).join("\n");
  return `${header}\n\n${list}`;
}


