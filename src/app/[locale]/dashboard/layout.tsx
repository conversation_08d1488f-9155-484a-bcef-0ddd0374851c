"use client";

import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useEffect } from "react";
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { DtcSidebar } from "@/components/ui/navigation/DtcSidebar";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user: authUser, loading: authLoading } = useAuth();
  const { loading: userLoading } = useUser();
  const router = useRouter();
  const locale = useLocale();

  // Combined loading state
  const isLoading = authLoading || userLoading;

  useEffect(() => {
    if (!authLoading && !authUser) {
      router.push(`/${locale}/login`);
    }
  }, [authUser, authLoading, router, locale]);

  // Show loading while checking auth or fetching user profile
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-primary-deep/10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-charcoal font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated
  if (!authUser) {
    return null; // Will redirect to login
  }

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-muted/30 to-primary/5">
        <DtcSidebar />
        <SidebarInset className="flex-1">
          {/* Mobile floating hamburger (no banner) */}
          <SidebarTrigger
            className={`md:hidden fixed z-30 top-4 ${locale === 'ar' ? 'right-4' : 'left-4'} h-10 w-10 rounded-full shadow-lg text-white bg-gradient-to-r from-primary to-primary-deep ring-1 ring-white/10 hover:brightness-110`}
          />
          <main className="flex-1 p-6 pt-2 md:pt-6">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
