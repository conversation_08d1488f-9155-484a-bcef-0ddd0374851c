"use client";

import { useUser } from "@/hooks/useUser";
import { DtcHero } from "@/components/ui/Hero";

export default function DashboardPage() {
  const { user } = useUser();

  return (
    <div className="space-y-6">
      <DtcHero
        title="Welcome to DTC Portal"
        subtitle={user ? `Hello ${user.displayName || user.email?.split('@')[0]}, welcome to your dashboard.` : undefined}
        image="hero2"
      />
    </div>
  );
}
