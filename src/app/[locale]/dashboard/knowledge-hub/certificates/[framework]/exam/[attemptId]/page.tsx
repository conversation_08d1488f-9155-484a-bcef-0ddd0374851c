"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { DtcHero } from "@/components/ui/Hero";
import { useUser } from "@/hooks/useUser";
import { getExamAttempt, type ExamAttempt } from "@/Services/examAttemptsService";
import { getQuestion, type QuestionRecord } from "@/Services/questionsService";
import ExamInterface from "@/components/ui/ExamAssets/ExamInterface";

export default function ExamPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const { user } = useUser();
  
  const [attempt, setAttempt] = useState<ExamAttempt | null>(null);
  const [questions, setQuestions] = useState<QuestionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const attemptId = params?.attemptId as string;
  const certificateId = params?.framework as string;

  useEffect(() => {
    if (user && attemptId) {
      loadExamData();
    }
  }, [user, attemptId]);

  const loadExamData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Load the exam attempt
      const examAttempt = await getExamAttempt(user.uid, attemptId);
      if (!examAttempt) {
        setError("Exam attempt not found");
        return;
      }

      // Verify the attempt belongs to the correct certificate
      if (examAttempt.certificateId !== certificateId) {
        setError("Invalid exam attempt");
        return;
      }

      setAttempt(examAttempt);

      // Load all questions for this attempt
      const questionPromises = examAttempt.questionIds.map(questionId => 
        getQuestion(certificateId, questionId)
      );
      
      const loadedQuestions = await Promise.all(questionPromises);
      const validQuestions = loadedQuestions.filter(q => q !== null) as QuestionRecord[];
      
      if (validQuestions.length !== examAttempt.questionIds.length) {
        console.warn("Some questions could not be loaded");
      }

      setQuestions(validQuestions);
    } catch (err) {
      console.error("Error loading exam data:", err);
      setError("Failed to load exam data");
    } finally {
      setLoading(false);
    }
  };

  const handleExamComplete = () => {
    // Navigate back to certificate page
    router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}`);
  };

  const handleExamExit = () => {
    // Navigate back to certificate page
    router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DtcHero
          title="Loading Examination"
          subtitle="Preparing your exam session..."
          image="hero2"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-16">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <p className="text-grey text-lg">Loading examination data...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !attempt) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DtcHero
          title="Examination Error"
          subtitle="Unable to load the requested exam"
          image="hero2"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <p className="text-red-600 text-lg mb-4">{error || "Exam not found"}</p>
            <button
              onClick={() => router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}`)}
              className="bg-primary hover:bg-primary-deep text-white px-6 py-3 rounded-lg font-medium"
            >
              Return to Certificate
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-muted/30">
      <DtcHero
        title={`${attempt.configuration.mode === "practice" ? "Practice" : attempt.configuration.mode === "realExam" ? "Real Exam" : "Question Bank"} Mode`}
        subtitle={`Attempt ID: ${attempt.id}`}
        image="hero2"
      />
      
      <ExamInterface
        attempt={attempt}
        questions={questions}
        onComplete={handleExamComplete}
        onExit={handleExamExit}
        onAttemptUpdate={setAttempt}
      />
    </div>
  );
}
