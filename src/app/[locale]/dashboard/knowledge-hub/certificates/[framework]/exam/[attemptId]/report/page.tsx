"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { DtcHero } from "@/components/ui/Hero";
import { useUser } from "@/hooks/useUser";
import { getExamAttempt, getAttemptSummaries, type ExamAttempt } from "@/Services/examAttemptsService";
import { getQuestion, type QuestionRecord } from "@/Services/questionsService";
import { PerformanceReport } from "@/components/ui/ExamAssets/PerformanceReport";

export default function ExamReportPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const { user } = useUser();
  
  const [attempt, setAttempt] = useState<ExamAttempt | null>(null);
  const [questions, setQuestions] = useState<QuestionRecord[]>([]);
  const [previousAttempts, setPreviousAttempts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const attemptId = params?.attemptId as string;
  const certificateId = params?.framework as string;

  useEffect(() => {
    if (user && attemptId) {
      loadReportData();
    }
  }, [user, attemptId]);

  const loadReportData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Load the exam attempt
      const examAttempt = await getExamAttempt(user.uid, attemptId);
      if (!examAttempt) {
        setError("Exam attempt not found");
        return;
      }

      // Verify the attempt belongs to the correct certificate and is completed
      if (examAttempt.certificateId !== certificateId) {
        setError("Invalid exam attempt");
        return;
      }

      if (examAttempt.status !== "completed") {
        setError("Exam not completed yet");
        return;
      }

      setAttempt(examAttempt);

      // Load all questions for this attempt
      const questionPromises = examAttempt.questionIds.map(questionId => 
        getQuestion(certificateId, questionId)
      );
      
      const loadedQuestions = await Promise.all(questionPromises);
      const validQuestions = loadedQuestions.filter(q => q !== null) as QuestionRecord[];
      
      setQuestions(validQuestions);

      // Load previous attempts for comparison
      try {
        const allAttempts = await getAttemptSummaries(user.uid, certificateId);
        const previousOnly = allAttempts.filter(a => a.id !== attemptId);
        setPreviousAttempts(previousOnly);
      } catch (err) {
        console.warn("Could not load previous attempts:", err);
      }
    } catch (err) {
      console.error("Error loading report data:", err);
      setError("Failed to load performance report");
    } finally {
      setLoading(false);
    }
  };

  const handleRetakeExam = () => {
    router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}`);
  };

  const handleBackToCertificate = () => {
    router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DtcHero
          title="Loading Performance Report"
          subtitle="Analyzing your exam results..."
          image="hero2"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-16">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <p className="text-grey text-lg">Generating your performance analytics...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !attempt) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DtcHero
          title="Report Error"
          subtitle="Unable to load the performance report"
          image="hero2"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <p className="text-red-600 text-lg mb-4">{error || "Report not found"}</p>
            <button
              onClick={handleBackToCertificate}
              className="bg-primary hover:bg-primary-deep text-white px-6 py-3 rounded-lg font-medium"
            >
              Return to Certificate
            </button>
          </div>
        </div>
      </div>
    );
  }

  const modeTitle = attempt.configuration.mode === "practice" 
    ? "Practice Mode" 
    : attempt.configuration.mode === "realExam" 
    ? "Real Exam Mode" 
    : "Question Bank Mode";

  return (
    <div className="min-h-screen bg-muted/30">
      <DtcHero
        title="Performance Report"
        subtitle={`${modeTitle} - ${attempt.score}% Score`}
        image="hero2"
      />
      
      <PerformanceReport
        attempt={attempt}
        questions={questions}
        previousAttempts={previousAttempts}
        onRetakeExam={handleRetakeExam}
        onBackToCertificate={handleBackToCertificate}
      />
    </div>
  );
}
