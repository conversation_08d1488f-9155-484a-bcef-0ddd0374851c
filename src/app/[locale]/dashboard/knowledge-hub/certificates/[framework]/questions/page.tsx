"use client";

import { useTranslations } from "next-intl";
import { useUser } from "@/hooks/useUser";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { DtcHero } from "@/components/ui/Hero";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import ImportQuestionsModal, { type ParsedQuestion, type ImportMode } from "@/components/ui/knowledge-hub/Certificates/questions/ImportQuestionsModal";
import { useQuestions, useQuestionsActions } from "@/hooks/useQuestions";
import QuestionsTable from "@/components/ui/knowledge-hub/Certificates/questions/QuestionsTable";
import { toast } from "sonner";
import { type QuestionInput } from "@/Services/questionsService";
import { updateQuestionCategories } from "@/Services/questionsService";
import CategorizeQuestionsWizard, { type CategorizeMode } from "@/components/ui/knowledge-hub/Certificates/questions/CategorizeQuestionsWizard";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import { getCertificateTaxonomy, setCertificateTaxonomy, type CertificateTaxonomy } from "@/Services/questionsService";
import { ConfirmModal } from "@/components/ui/DtcModal";

export default function QuestionsListPage() {
  const { user } = useUser();
  const t = useTranslations("knowledgeHub.certificates");
  const router = useRouter();
  const params = useParams();

  useEffect(() => {
    if (user && user.role !== "Admin") {
      router.back();
    }
  }, [user, router]);

  if (!user || user.role !== "Admin") return null;

  // Get certificate ID from URL parameter
  const certificateId = String(params?.framework || "");
  const frameworkTitle = certificateId.replace(/-/g, " ");

  const [open, setOpen] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState({ current: 0, total: 0 });
  const [categorizeOpen, setCategorizeOpen] = useState(false);
  const [certificateInfo, setCertificateInfo] = useState<CertificateRecord | null>(null);
  const [taxonomy, setTaxonomy] = useState<CertificateTaxonomy | null>(null);
  const [clearConfirmOpen, setClearConfirmOpen] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [clearProgress, setClearProgress] = useState<{ current: number; total: number }>({ current: 0, total: 0 });

  useEffect(() => {
    let mounted = true;
    (async () => {
      if (!certificateId) return;
      try {
        const cert = await getCertificate(certificateId);
        if (mounted) setCertificateInfo(cert);
        const tx = await getCertificateTaxonomy(certificateId);
        if (mounted) setTaxonomy(tx);
      } catch (e) {
        // ignore
      }
    })();
    return () => { mounted = false; };
  }, [certificateId]);

  // Use the questions hooks
  const { data: questions, loading } = useQuestions(certificateId);
  const { deleteQuestion, updateQuestion, bulkCreateQuestions } = useQuestionsActions(certificateId);

  const handleImportQuestions = async (mode: ImportMode, parsedQuestions: ParsedQuestion[]) => {
    setImporting(true);
    setImportProgress({ current: 0, total: parsedQuestions.length });

    try {
      // Convert ParsedQuestion to QuestionInput format
      const questionsToImport: QuestionInput[] = parsedQuestions.map((q, index) => ({
        question: q.question,
        correct: q.correct,
        choices: q.choices,
        rowNumber: q.row, // Use the original row number from the parsed data
      }));

      // Import questions one by one to show progress
      const questionIds: string[] = [];
      for (let i = 0; i < questionsToImport.length; i++) {
        const question = questionsToImport[i];

        // Update progress
        setImportProgress({ current: i, total: questionsToImport.length });

        // Import the question using the service
        const { createQuestion } = await import("@/Services/questionsService");
        const questionId = await createQuestion(certificateId, question);

        questionIds.push(questionId);

        // Small delay to make progress visible and avoid overwhelming Firestore
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Final progress update
      setImportProgress({ current: questionsToImport.length, total: questionsToImport.length });

      toast.success(`Successfully imported ${questionIds.length} questions`);
      setOpen(false);
    } catch (error) {
      console.error("Error importing questions:", error);
      toast.error("Failed to import questions");
    } finally {
      setImporting(false);
      setImportProgress({ current: 0, total: 0 });
    }
  };

  const handleDeleteQuestion = async (questionId: string): Promise<boolean> => {
    return await deleteQuestion(questionId);
  };

  const handleEditQuestion = async (questionId: string, data: Partial<QuestionInput>): Promise<boolean> => {
    return await updateQuestion(questionId, data);
  };

  // AI calls
  const callSuggestGroups = async ({ certificate, samples }: { certificate: any; samples: { id: string; question: string }[] }) => {
    const res = await fetch(`/api/ai/questions/suggest-groups`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ certificate, samples })
    });
    if (!res.ok) throw new Error('Failed to get suggestions');
    return await res.json();
  };

  const callCategorizeAI = async ({ mode, certificate, questionItems, selectedQuestionIds, customGroups }: {
    mode: CategorizeMode; certificate: any; questionItems: { id: string; question: string }[]; selectedQuestionIds: string[]; customGroups?: string[];
  }) => {
    const res = await fetch(`/api/ai/questions/categorize`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        mode,
        certificate,
        questionItems,
        selectedQuestionIds,
        customGroups,
        allowedGroups: taxonomy?.groups ?? [],
        maxTags: 1,
        strict: true,
      })
    });
    if (!res.ok) throw new Error('Failed to categorize');
    return await res.json();
  };

  const persistUpdates = async (updates: Array<{ id: string; tags: string[]; topic?: string; difficulty?: 'Easy'|'Medium'|'Hard' }>, onProgress: (done: number, total: number) => void) => {
    const total = updates.length;
    let done = 0;
    for (const u of updates) {
      await updateQuestionCategories(certificateId, u.id, { tags: u.tags, topic: u.topic, difficulty: u.difficulty }, true);
      done += 1;
      onProgress(done, total);
      await new Promise(r => setTimeout(r, 50));
    }
  };

  const handleClearAllTags = async () => {
    try {
      setClearing(true);
      setClearProgress({ current: 0, total: questions.length });
      let idx = 0;
      for (const q of questions) {
        await updateQuestionCategories(certificateId, q.id, { tags: [] }, false);
        idx += 1;
        setClearProgress({ current: idx, total: questions.length });
        // small delay to keep UI responsive
        await new Promise(r => setTimeout(r, 20));
      }
      toast.success("All tags cleared");
    } catch (e) {
      toast.error("Failed to clear tags");
    } finally {
      setClearing(false);
      setClearConfirmOpen(false);
      setClearProgress({ current: 0, total: 0 });
    }
  };

  const handleApplyCategories = async (_payload: { mode: CategorizeMode; selectedQuestionIds: string[]; suggestedGroups: string[]; customGroups?: string[]; }) => {
    // No-op here because we persist inside callCategorizeAI once we get model outputs
  };

  return (
    <div className="space-y-6">
      {(() => {
        const displayName = (certificateInfo?.name ?? frameworkTitle).toUpperCase();
        return (
          <DtcHero title={`${displayName} Questions`} subtitle={t("heroSubtitle")} image="hero2">
            <Button
              onClick={() => setOpen(true)}
              className="h-12 rounded-xl bg-white px-6 text-base font-semibold text-gray-900 shadow-sm hover:bg-white/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/60"
            >
              <Upload className="mr-2 h-5 w-5" /> Import Questions
            </Button>
            {questions.length > 0 && (
              <Button
                onClick={() => setCategorizeOpen(true)}
                className="ml-3 h-12 rounded-xl bg-white px-6 text-base font-semibold text-gray-900 shadow-sm hover:bg-white/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/60"
              >
                Categorize Questions
              </Button>
            )}
            {questions.length > 0 && (
              <Button
                onClick={() => setClearConfirmOpen(true)}
                disabled={clearing}
                className="ml-3 h-12 rounded-xl bg-white px-6 text-base font-semibold text-gray-900 shadow-sm hover:bg-white/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/60"
              >
                {clearing ? `Clearing ${clearProgress.current}/${clearProgress.total}` : "Clear All Tags"}
              </Button>
            )}
          </DtcHero>
        );
      })()}

      {/* Questions Table */}
      <QuestionsTable
        questions={questions}
        loading={loading}
        onEdit={handleEditQuestion}
        onDelete={handleDeleteQuestion}
      />

      <ImportQuestionsModal
        open={open}
        onClose={() => !importing && setOpen(false)}
        onConfirm={handleImportQuestions}
        importing={importing}
        importProgress={importProgress}
      />

      <CategorizeQuestionsWizard
        isOpen={categorizeOpen}
        onClose={() => setCategorizeOpen(false)}
        certificate={{
          id: certificateId,
          name: certificateInfo?.name ?? frameworkTitle,
          provider: certificateInfo?.provider ?? "",
          description: certificateInfo?.description ?? "",
        }}
        questions={questions}
        onSuggestGroupsWithAI={callSuggestGroups}
        onCallCategorizationAI={callCategorizeAI}
        onPersistUpdates={persistUpdates}
        // taxonomy props
        initialGroups={taxonomy?.groups ?? []}
        onSaveGroups={async (groups: string[]) => {
          await setCertificateTaxonomy(certificateId, { groups, version: (taxonomy?.version ?? 0) + 1, strict: true, maxTagsPerQuestion: 1 });
          setTaxonomy({ groups, version: (taxonomy?.version ?? 0) + 1, strict: true, maxTagsPerQuestion: 1 });
        }}
      />

      <ConfirmModal
        isOpen={clearConfirmOpen}
        onClose={() => setClearConfirmOpen(false)}
        onConfirm={handleClearAllTags}
        title="Clear all tags"
        description="This will remove all tags from all questions for this certificate. This action cannot be undone."
        confirmText={clearing ? `Clearing ${clearProgress.current}/${clearProgress.total}` : "Clear"}
        loading={clearing}
        variant="destructive"
      />
    </div>
  );
}


