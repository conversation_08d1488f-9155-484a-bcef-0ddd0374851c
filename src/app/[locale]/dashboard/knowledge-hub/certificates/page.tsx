"use client";

import { useState, useEffect, useRef } from "react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { DtcHero } from "@/components/ui/Hero";
import { useUser } from "@/hooks/useUser";
import AddCertificateModal from "@/components/ui/knowledge-hub/Certificates/AddCertificateModal";
import { Plus } from "lucide-react";
import { listCertificates, type CertificateRecord } from "@/Services/certificateDetails";
import CertificateCard from "@/components/ui/knowledge-hub/Certificates/CertificateCard";

export default function CertificatesPage() {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("knowledgeHub.certificates");
  const [open, setOpen] = useState(false);
  const [editTarget, setEditTarget] = useState<CertificateRecord | null>(null);
  const [items, setItems] = useState<CertificateRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const itemsRef = useRef<CertificateRecord[]>([]);

  useEffect(() => {
    itemsRef.current = items;
  }, [items]);

  useEffect(() => {
    // Allow Admin and DTC to view; redirect others
    if (user && user.role !== "Admin" && user.role !== "DTC") {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, router, locale]);

  useEffect(() => {
    let active = true;
    async function load() {
      try {
        setLoading(true);
        const data = await listCertificates();
        if (!active) return;
        setItems(data);
      } finally {
        if (active) setLoading(false);
      }
    }
    load();
    function onDeleted(e: Event) {
      const id = (e as CustomEvent).detail?.id as string;
      if (!id) return;
      setItems((prev) => prev.filter((x) => x.id !== id));
    }
    function onEdit(e: Event) {
      const id = (e as CustomEvent).detail?.id as string;
      if (!id) return;
      const record = itemsRef.current.find((x) => x.id === id) || null;
      if (record) {
        setEditTarget(record);
        setOpen(true);
      }
    }
    window.addEventListener("certificate:deleted", onDeleted as EventListener);
    window.addEventListener("certificate:edit", onEdit as EventListener);
    return () => { active = false; window.removeEventListener("certificate:deleted", onDeleted as EventListener); window.removeEventListener("certificate:edit", onEdit as EventListener); };
  }, []);

  if (!user) return null;

  const canManage = user.role === "Admin";

  return (
    <div className="space-y-6">
      <DtcHero title={t("heroTitle")} subtitle={t("heroSubtitle")} image="hero1">
        {canManage && (
          <Button
            onClick={() => setOpen(true)}
            className="h-12 rounded-xl bg-white px-6 text-base font-semibold text-gray-900 shadow-sm hover:bg-white/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/60"
          >
            <Plus className="mr-2 h-5 w-5" /> {t("add")}
          </Button>
        )}
      </DtcHero>

      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">{t("heroTitle")}</h2>
      </div>

      {loading ? (
        <div className="rounded-xl border border-gray-100 bg-white p-8 text-center text-gray-500">Loading…</div>
      ) : items.length === 0 ? (
        <div className="rounded-xl border border-gray-100 bg-white p-8 text-center text-gray-500">No certificates yet.</div>
      ) : (
        <ul className="grid gap-8 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2">
          {items.map((c) => (
            <li key={c.id}>
              <CertificateCard certificate={c} />
            </li>
          ))}
        </ul>
      )}

      {canManage && (
        <AddCertificateModal
          isOpen={open}
          onClose={() => { setOpen(false); setEditTarget(null); }}
          currentUserUid={user.uid}
          editCertificate={editTarget}
          onUpdated={async () => {
            setItems(await listCertificates());
            setEditTarget(null);
          }}
          onCreated={() => {
            (async () => setItems(await listCertificates()))();
          }}
        />
      )}
    </div>
  );
}


