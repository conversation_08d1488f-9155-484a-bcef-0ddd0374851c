"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/hooks/useUser";
import { useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Users, Shield, UserPlus, Search, RefreshCw } from "lucide-react";
import { DtcHero } from "@/components/ui/Hero";
import { UserTable } from "@/components/users/UserTable";
import { UserModal, type UserFormData } from "@/components/users/UserModal";
import { ConfirmModal } from "@/components/ui/DtcModal";
import { type UserProfile } from "@/Services/userService";
import { toast } from "sonner";
// import { Select, SelectContent, SelectI<PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function UserManagementPage() {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('users.manage');

  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<'all' | 'Admin' | 'DTC'>('all');

  // Modal states
  const [userModalOpen, setUserModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  // Redirect if user is not Admin
  useEffect(() => {
    if (user && user.role !== "Admin") {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, router, locale]);

  // Fetch users directly from Firestore
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { getAllUsers } = await import('@/Services/userService');
      const allUsers = await getAllUsers();
      setUsers(allUsers);
      setFilteredUsers(allUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Error fetching users');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.role === "Admin") {
      fetchUsers();
    }
  }, [user]);

  // Show loading state while checking user permissions
  const showLoadingPlaceholder = !user;

  // Filter users based on search term and role
  useEffect(() => {
    const normalized = searchTerm.trim().toLowerCase();
    let filtered = users;
    if (normalized) {
      filtered = filtered.filter(u =>
        u.displayName?.toLowerCase().includes(normalized) ||
        u.email?.toLowerCase().includes(normalized) ||
        u.firstName?.toLowerCase().includes(normalized) ||
        u.lastName?.toLowerCase().includes(normalized)
      );
    }
    if (roleFilter !== 'all') {
      filtered = filtered.filter(u => u.role === roleFilter);
    }
    setFilteredUsers(filtered);
  }, [searchTerm, roleFilter, users]);

  // Handle create user directly
  const handleCreateUser = async (userData: UserFormData) => {
    try {
      const { adminCreateUserWithEmailAndPassword } = await import('@/Services/authService');
      const { createDoc } = await import('@/Services/firestoreService');

      // Create Firebase Auth user first
      const firebaseUser = await adminCreateUserWithEmailAndPassword(
        { email: userData.email, password: userData.password! },
        userData.displayName || `${userData.firstName} ${userData.lastName}`.trim()
      );

      // Create user profile in Firestore
      const userProfileData = {
        uid: firebaseUser.uid,
        email: userData.email,
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`.trim() || null,
        photoURL: userData.photoURL || null,
        firstName: userData.firstName || null,
        lastName: userData.lastName || null,
        phoneNumber: userData.phoneNumber || null,
        emailVerified: true,
        provider: 'email' as const,
        role: userData.role,
        lastLoginAt: new Date(),
        preferences: {
          language: 'en',
          theme: 'light',
          notifications: true,
        },
      };

      const userPath = `users/${firebaseUser.uid}`;
      await createDoc(userPath, userProfileData);

      toast.success('User created successfully');
      fetchUsers(); // Refresh the list
    } catch (error: unknown) {
      console.error('Error creating user:', error);
      const message = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to create user: ${message}`);
    }
  };

  // Handle edit user directly
  const handleEditUser = async (userData: UserFormData) => {
    if (!selectedUser) return;

    try {
      const { updateUserProfile } = await import('@/Services/userService');

      // Prepare update data
      const updateData = {
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`.trim() || null,
        photoURL: userData.photoURL || null,
        firstName: userData.firstName || null,
        lastName: userData.lastName || null,
        phoneNumber: userData.phoneNumber || null,
        role: userData.role,
      };

      await updateUserProfile(selectedUser.uid, updateData);

      toast.success('User updated successfully');
      fetchUsers(); // Refresh the list
    } catch (error: unknown) {
      console.error('Error updating user:', error);
      const message = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to update user: ${message}`);
    }
  };

  // Handle delete user (using API as required)
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      const response = await fetch(`/api/users/${selectedUser.uid}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('User deleted successfully from database');
        fetchUsers(); // Refresh the list
        setDeleteModalOpen(false);
        setSelectedUser(null);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Error deleting user');
    }
  };

  // Modal handlers
  const openCreateModal = () => {
    setSelectedUser(null);
    setModalMode('create');
    setUserModalOpen(true);
  };

  const openEditModal = (user: UserProfile) => {
    setSelectedUser(user);
    setModalMode('edit');
    setUserModalOpen(true);
  };

  const openDeleteModal = (user: UserProfile) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  };

  if (showLoadingPlaceholder) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-primary/10 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-primary/10 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!user || user.role !== "Admin") {
    return (
      <div className="space-y-6">
        <Alert className="border-destructive/20 bg-destructive/5">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only available to Admin users.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DtcHero
        title={t('heroTitle')}
        subtitle={t('heroSubtitle')}
        image="hero3"
      />

      {/* DTC Gradient Toolbar */}
      <div className="rounded-xl bg-gradient-to-r from-primary to-primary-deep text-white shadow-md ring-1 ring-white/10">
        <div className="p-5 flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-white/90" />
            <div className="font-display text-white/95 text-base tracking-wide">
              {t('title')} <span className="opacity-80">({filteredUsers.length})</span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
            {/* Search */}
            <div className="relative flex-1 min-w-[220px]">
              <Search className="absolute left-3 top-3 h-4 w-4 text-white/70 pointer-events-none" />
              <Input
                placeholder={t('searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 text-white placeholder:text-white/70 border-white/20 focus:border-white/40 focus:ring-0 transition-all duration-200 focus:scale-[1.02] focus:shadow-[0_0_0_6px_rgba(255,255,255,0.08)]"
              />
            </div>

            {/* Role Filter (Chips) */}
            <div className="flex items-center gap-2">
              {[{key:'all', label:t('filters.role.all')},{key:'Admin', label:t('filters.role.admin')},{key:'DTC', label:t('filters.role.dtc')}].map(({key,label}) => (
                <button
                  key={key}
                  type="button"
                  aria-pressed={roleFilter === (key as 'all'|'Admin'|'DTC')}
                  onClick={() => setRoleFilter(key as 'all'|'Admin'|'DTC')}
                  className={
                    `px-3 h-9 rounded-full text-sm transition-all duration-200 border ` +
                    (roleFilter === key
                      ? `bg-white text-primary border-white/80 shadow-sm`
                      : `bg-white/10 text-white/90 border-white/20 hover:bg-white/15`)
                  }
                >
                  {label}
                </button>
              ))}
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <Button
                variant="ghost"
                onClick={fetchUsers}
                disabled={loading}
                className="bg-white/10 text-white hover:bg-white/15"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {t('refresh')}
              </Button>
              <Button
                onClick={openCreateModal}
                className="bg-white text-primary hover:bg-white/90 shadow-sm transition-transform duration-200 hover:scale-[1.02]"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                {t('addUser')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Users Table - simplified container, no large header */}
      <div className="bg-white border border-border/50 rounded-xl shadow-sm overflow-hidden">
        <UserTable
          users={filteredUsers}
          onEditUser={openEditModal}
          onDeleteUser={openDeleteModal}
          loading={loading}
        />
      </div>

      {/* User Modal */}
      <UserModal
        isOpen={userModalOpen}
        onClose={() => setUserModalOpen(false)}
        onSave={modalMode === 'create' ? handleCreateUser : handleEditUser}
        user={selectedUser}
        mode={modalMode}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteUser}
        title="Delete User"
        description={`Are you sure you want to delete ${selectedUser?.displayName || selectedUser?.email}? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  );
}