"use client";

import { useEffect, useState } from "react";
import { onSnapshot, query, type DocumentData } from "firebase/firestore";
import { getCollectionRef } from "@/Services/firestoreService";
import { 
  listQuestions, 
  createQuestion, 
  updateQuestion, 
  deleteQuestion, 
  bulkCreateQuestions,
  getQuestionsCount,
  type QuestionRecord, 
  type QuestionInput 
} from "@/Services/questionsService";

/**
 * Hook for real-time questions data for a specific certificate
 */
export function useQuestions(certificateId: string) {
  const [data, setData] = useState<QuestionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!certificateId) {
      setData([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    const path = `certificates/${certificateId}/questions`;
    const q = query(getCollectionRef<QuestionRecord>(path));
    
    const unsub = onSnapshot(
      q,
      (snap) => {
        const questions = snap.docs.map((d) => ({ id: d.id, ...(d.data() as QuestionRecord) }) as QuestionRecord);
        
        // Sort by rowNumber if available, otherwise by creation date
        const sortedQuestions = questions.sort((a, b) => {
          if (a.rowNumber !== undefined && b.rowNumber !== undefined) {
            return a.rowNumber - b.rowNumber;
          }
          return Number(new Date(b.createdAt || 0)) - Number(new Date(a.createdAt || 0));
        });
        
        setData(sortedQuestions);
        setLoading(false);
      },
      (err) => {
        setError(err);
        setLoading(false);
      }
    );
    
    return () => unsub();
  }, [certificateId]);

  return { data, loading, error } as const;
}

/**
 * Hook for questions management operations
 */
export function useQuestionsActions(certificateId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createQuestionAction = async (input: QuestionInput): Promise<string | null> => {
    try {
      setLoading(true);
      setError(null);
      const questionId = await createQuestion(certificateId, input);
      return questionId;
    } catch (err) {
      setError(err as Error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateQuestionAction = async (questionId: string, data: Partial<QuestionInput>): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      await updateQuestion(certificateId, questionId, data);
      return true;
    } catch (err) {
      setError(err as Error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const deleteQuestionAction = async (questionId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      await deleteQuestion(certificateId, questionId);
      return true;
    } catch (err) {
      setError(err as Error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const bulkCreateQuestionsAction = async (questions: QuestionInput[]): Promise<string[] | null> => {
    try {
      setLoading(true);
      setError(null);
      const questionIds = await bulkCreateQuestions(certificateId, questions);
      return questionIds;
    } catch (err) {
      setError(err as Error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const getCountAction = async (): Promise<number> => {
    try {
      return await getQuestionsCount(certificateId);
    } catch (err) {
      setError(err as Error);
      return 0;
    }
  };

  return {
    loading,
    error,
    createQuestion: createQuestionAction,
    updateQuestion: updateQuestionAction,
    deleteQuestion: deleteQuestionAction,
    bulkCreateQuestions: bulkCreateQuestionsAction,
    getCount: getCountAction,
  } as const;
}
