"use client";
import { useEffect, useState } from "react";
import { onSnapshot, query, type DocumentData } from "firebase/firestore";
import { getCollectionRef } from "@/Services/firestoreService";

export function useFirestoreCollection<T = DocumentData>(path: string) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    setLoading(true);
    const q = query(getCollectionRef<T>(path));
    const unsub = onSnapshot(
      q,
      (snap) => {
        setData(snap.docs.map((d) => ({ id: d.id, ...(d.data() as T) }) as T));
        setLoading(false);
      },
      (err) => {
        setError(err);
        setLoading(false);
      }
    );
    return () => unsub();
  }, [path]);

  return { data, loading, error } as const;
}


