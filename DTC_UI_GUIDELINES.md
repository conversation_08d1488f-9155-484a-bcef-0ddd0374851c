## DTC UI Guidelines (v0.1)

Design principles
- Elegance over noise: clean white backgrounds, soft borders, subtle shadows.
- No black overlays, no cartoonish icons, no harsh borders.
- Consistent typography: Montserrat (EN), Greta Text Arabic (AR), Glancyr for display.
- RTL parity: Arabic uses same spacing/rhythm mirrored; long text wraps.

Color system
- Use semantic tokens only (from globals.css):
  - Primary: --color-primary, --color-primary-deep
  - Text: --color-foreground, --color-charcoal
  - Surfaces: --color-white, --color-muted
  - Borders: --color-border
  - Accents: --color-accent, --color-accent-lime

Hard rules
- Borders: never use black borders. Avoid classes like `border-black`. Use `border-[--color-border]`.
- Icons: avoid cartoonish/clipart icons. Prefer minimal outline icons (e.g., Lucide) at 1.5px stroke.
- Modals: avoid full-screen black backdrops. Prefer inline expansion or slide-out panels.
- Buttons: use `BrandButton` variants (`primary | outline | ghost`)—no ad-hoc button styles.
- Cards: use `Card` component for containers; no custom shadow/border mixes per page.

Layout & spacing
- Use Tailwind spacing scale consistently (4/6/8 px multiples).
- Max content widths: 640–1280px depending on context. Avoid full-width long lines.
- Section rhythm: 48–64px vertical spacing between major sections.

Typography
- Headings: Glancyr (if available) or Montserrat, weights 600–700.
- Body: Montserrat 400–500; Arabic uses Greta Text Arabic 400–700.
- Long text: wrapping on by default; avoid truncation except on dense tables.

States & feedback
- Hover/active: subtle color shifts; no aggressive animations.
- Errors: use accessible reds; inline messages near fields; avoid toast-only errors.
- Loading: skeletons or subtle spinners; never block with opaque overlays.

Components to reuse
- Button: `src/components/ui/BrandButton.tsx`
- Card: `src/components/ui/Card.tsx`

Do/Don’t quick list
- Do: tokens, variants, shared components, RTL-aware CSS logical props.
- Don’t: black borders, cartoonish icons, ad-hoc one-off styles, duplicated UI patterns.


