rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isSignedIn() {
      return request.auth != null;
    }

    function isAdmin() {
      return isSignedIn() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'Admin';
    }

    // Certificates: readable by authenticated users, writable only by Admin
    match /certificates/{certId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();

      // Questions subcollection: readable by authenticated users, writable only by Admin
      match /questions/{questionId} {
        allow read: if isSignedIn();
        allow create, update, delete: if isAdmin();
      }
    }
    // Public read-only landing content example (optional):
    // match /public/{docId} {
    //   allow read: if true;
    //   allow write: if false;
    // }

    // Default: require authentication for other collections
    match /{document=**} {
      allow read, write: if isSignedIn();
    }
  }
}

